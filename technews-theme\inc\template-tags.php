<?php
/**
 * Custom template tags for this theme
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

if (!function_exists('technews_posted_on')) :
    /**
     * Prints HTML with meta information for the current post-date/time.
     */
    function technews_posted_on() {
        $time_string = '<time class="entry-date published updated" datetime="%1$s">%2$s</time>';
        if (get_the_time('U') !== get_the_modified_time('U')) {
            $time_string = '<time class="entry-date published" datetime="%1$s">%2$s</time><time class="updated" datetime="%3$s">%4$s</time>';
        }

        $time_string = sprintf($time_string,
            esc_attr(get_the_date(DATE_W3C)),
            esc_html(get_the_date()),
            esc_attr(get_the_modified_date(DATE_W3C)),
            esc_html(get_the_modified_date())
        );

        $posted_on = sprintf(
            esc_html_x('Posted on %s', 'post date', 'technews-theme'),
            '<a href="' . esc_url(get_permalink()) . '" rel="bookmark">' . $time_string . '</a>'
        );

        echo '<span class="posted-on">' . $posted_on . '</span>';
    }
endif;

if (!function_exists('technews_posted_by')) :
    /**
     * Prints HTML with meta information for the current author.
     */
    function technews_posted_by() {
        $byline = sprintf(
            esc_html_x('by %s', 'post author', 'technews-theme'),
            '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
        );

        echo '<span class="byline"> ' . $byline . '</span>';
    }
endif;

if (!function_exists('technews_entry_footer')) :
    /**
     * Prints HTML with meta information for the categories, tags and comments.
     */
    function technews_entry_footer() {
        // Hide category and tag text for pages.
        if ('post' === get_post_type()) {
            /* translators: used between list items, there is a space after the comma */
            $categories_list = get_the_category_list(esc_html__(', ', 'technews-theme'));
            if ($categories_list) {
                printf('<span class="cat-links">' . esc_html__('Posted in %1$s', 'technews-theme') . '</span>', $categories_list);
            }

            /* translators: used between list items, there is a space after the comma */
            $tags_list = get_the_tag_list('', esc_html_x(', ', 'list item separator', 'technews-theme'));
            if ($tags_list) {
                printf('<span class="tags-links">' . esc_html__('Tagged %1$s', 'technews-theme') . '</span>', $tags_list);
            }
        }

        if (!is_single() && !post_password_required() && (comments_open() || get_comments_number())) {
            echo '<span class="comments-link">';
            comments_popup_link(
                sprintf(
                    wp_kses(
                        __('Leave a Comment<span class="screen-reader-text"> on %s</span>', 'technews-theme'),
                        array('span' => array('class' => array()))
                    ),
                    get_the_title()
                )
            );
            echo '</span>';
        }

        edit_post_link(
            sprintf(
                wp_kses(
                    __('Edit <span class="screen-reader-text">"%s"</span>', 'technews-theme'),
                    array('span' => array('class' => array()))
                ),
                get_the_title()
            ),
            '<span class="edit-link">',
            '</span>'
        );
    }
endif;

if (!function_exists('technews_post_thumbnail')) :
    /**
     * Displays an optional post thumbnail.
     */
    function technews_post_thumbnail() {
        if (post_password_required() || is_attachment() || !has_post_thumbnail()) {
            return;
        }

        if (is_singular()) :
        ?>
            <div class="post-thumbnail">
                <?php the_post_thumbnail(); ?>
            </div>
        <?php else : ?>
            <a class="post-thumbnail" href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                <?php
                the_post_thumbnail('post-thumbnail', array(
                    'alt' => the_title_attribute(array('echo' => false)),
                ));
                ?>
            </a>
        <?php
        endif;
    }
endif;

if (!function_exists('technews_reading_time')) :
    /**
     * Calculate and display estimated reading time
     */
    function technews_reading_time() {
        $content = get_post_field('post_content', get_the_ID());
        $word_count = str_word_count(strip_tags($content));
        $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute

        if ($reading_time == 1) {
            return sprintf(esc_html__('%d min read', 'technews-theme'), $reading_time);
        } else {
            return sprintf(esc_html__('%d mins read', 'technews-theme'), $reading_time);
        }
    }
endif;

if (!function_exists('technews_social_sharing_buttons')) :
    /**
     * Display social sharing buttons
     */
    function technews_social_sharing_buttons() {
        $post_url = urlencode(get_permalink());
        $post_title = urlencode(get_the_title());
        $post_excerpt = urlencode(get_the_excerpt());
        
        echo '<div class="social-sharing-buttons">';
        
        // Facebook
        echo '<a href="https://www.facebook.com/sharer/sharer.php?u=' . $post_url . '" target="_blank" rel="noopener noreferrer" class="social-share-btn facebook" aria-label="' . esc_attr__('Share on Facebook', 'technews-theme') . '">';
        echo '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>';
        echo '</a>';
        
        // Twitter
        echo '<a href="https://twitter.com/intent/tweet?url=' . $post_url . '&text=' . $post_title . '" target="_blank" rel="noopener noreferrer" class="social-share-btn twitter" aria-label="' . esc_attr__('Share on Twitter', 'technews-theme') . '">';
        echo '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>';
        echo '</a>';
        
        // LinkedIn
        echo '<a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $post_url . '" target="_blank" rel="noopener noreferrer" class="social-share-btn linkedin" aria-label="' . esc_attr__('Share on LinkedIn', 'technews-theme') . '">';
        echo '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>';
        echo '</a>';
        
        // Email
        echo '<a href="mailto:?subject=' . $post_title . '&body=' . $post_excerpt . '%20' . $post_url . '" class="social-share-btn email" aria-label="' . esc_attr__('Share via Email', 'technews-theme') . '">';
        echo '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>';
        echo '</a>';
        
        echo '</div>';
    }
endif;

if (!function_exists('technews_breadcrumbs')) :
    /**
     * Display breadcrumb navigation
     */
    function technews_breadcrumbs() {
        if (is_front_page()) {
            return;
        }

        $separator = ' <span class="breadcrumb-separator">/</span> ';
        $home_title = esc_html__('Home', 'technews-theme');

        echo '<nav class="breadcrumbs" aria-label="' . esc_attr__('Breadcrumb Navigation', 'technews-theme') . '">';
        echo '<a href="' . esc_url(home_url('/')) . '">' . $home_title . '</a>';

        if (is_category() || is_single()) {
            echo $separator;
            the_category(' <span class="breadcrumb-separator">,</span> ');
            if (is_single()) {
                echo $separator;
                echo '<span class="current">' . get_the_title() . '</span>';
            }
        } elseif (is_page()) {
            echo $separator;
            echo '<span class="current">' . get_the_title() . '</span>';
        } elseif (is_search()) {
            echo $separator;
            echo '<span class="current">' . sprintf(esc_html__('Search Results for "%s"', 'technews-theme'), get_search_query()) . '</span>';
        } elseif (is_tag()) {
            echo $separator;
            echo '<span class="current">' . sprintf(esc_html__('Posts Tagged "%s"', 'technews-theme'), single_tag_title('', false)) . '</span>';
        } elseif (is_author()) {
            echo $separator;
            echo '<span class="current">' . sprintf(esc_html__('Author: %s', 'technews-theme'), get_the_author()) . '</span>';
        } elseif (is_day()) {
            echo $separator;
            echo '<span class="current">' . sprintf(esc_html__('Archive for %s', 'technews-theme'), get_the_date()) . '</span>';
        } elseif (is_month()) {
            echo $separator;
            echo '<span class="current">' . sprintf(esc_html__('Archive for %s', 'technews-theme'), get_the_date('F Y')) . '</span>';
        } elseif (is_year()) {
            echo $separator;
            echo '<span class="current">' . sprintf(esc_html__('Archive for %s', 'technews-theme'), get_the_date('Y')) . '</span>';
        } elseif (is_404()) {
            echo $separator;
            echo '<span class="current">' . esc_html__('Error 404', 'technews-theme') . '</span>';
        }

        echo '</nav>';
    }
endif;
