/*
Theme Name: TechNews Pro
Description: Gutenberg block styles for TechNews Pro theme
Author: WordPress Theme Developer
Version: 1.0.0
*/

/* ==========================================================================
   Gutenberg Block Styles
   ========================================================================== */

/* Block Editor Wrapper */
.wp-block {
    max-width: 100%;
}

/* Wide and Full Width Blocks */
.alignwide {
    margin-left: auto;
    margin-right: auto;
    clear: both;
}

.alignfull {
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
    width: auto;
    max-width: 1000%;
    clear: both;
}

@media (min-width: 768px) {
    .alignwide {
        width: 120%;
        max-width: 120%;
        margin-left: -10%;
        margin-right: -10%;
    }
}

/* ==========================================================================
   Typography Blocks
   ========================================================================== */

/* Paragraph Block */
.wp-block-paragraph {
    margin-bottom: 1.5rem;
}

.wp-block-paragraph.has-drop-cap:not(:focus)::first-letter {
    float: left;
    font-size: 8.4rem;
    line-height: 0.68;
    font-weight: 100;
    margin: 0.05em 0.1em 0 0;
    text-transform: uppercase;
    font-style: normal;
    color: #007cba;
}

/* Heading Blocks */
.wp-block-heading {
    margin-top: 2rem;
    margin-bottom: 1rem;
    clear: both;
}

.wp-block-heading:first-child {
    margin-top: 0;
}

/* Quote Block */
.wp-block-quote {
    background-color: #f8f9fa;
    border-left: 4px solid #007cba;
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
}

.wp-block-quote p {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.wp-block-quote cite {
    display: block;
    font-size: 0.875rem;
    font-style: normal;
    color: #666666;
    margin-top: 1rem;
}

.wp-block-quote cite::before {
    content: "— ";
}

/* Pullquote Block */
.wp-block-pullquote {
    padding: 3rem 0;
    margin: 2rem 0;
    text-align: center;
    border-top: 4px solid #007cba;
    border-bottom: 4px solid #007cba;
}

.wp-block-pullquote blockquote {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-size: 1.75rem;
    line-height: 1.4;
    font-weight: 600;
}

.wp-block-pullquote cite {
    font-size: 1rem;
    color: #666666;
    margin-top: 1rem;
}

/* ==========================================================================
   Media Blocks
   ========================================================================== */

/* Image Block */
.wp-block-image {
    margin-bottom: 2rem;
}

.wp-block-image img {
    height: auto;
    max-width: 100%;
    border-radius: 8px;
}

.wp-block-image figcaption {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #666666;
    text-align: center;
    font-style: italic;
}

/* Gallery Block */
.wp-block-gallery {
    margin-bottom: 2rem;
}

.wp-block-gallery .blocks-gallery-grid {
    gap: 1rem;
}

.wp-block-gallery .blocks-gallery-item img {
    border-radius: 8px;
}

/* Cover Block */
.wp-block-cover {
    margin-bottom: 2rem;
    border-radius: 8px;
    overflow: hidden;
}

.wp-block-cover .wp-block-cover__inner-container {
    padding: 3rem;
}

/* Video Block */
.wp-block-video {
    margin-bottom: 2rem;
}

.wp-block-video video {
    border-radius: 8px;
}

/* ==========================================================================
   Layout Blocks
   ========================================================================== */

/* Group Block */
.wp-block-group {
    margin-bottom: 2rem;
}

.wp-block-group.has-background {
    padding: 2rem;
    border-radius: 8px;
}

/* Columns Block */
.wp-block-columns {
    margin-bottom: 2rem;
}

.wp-block-column {
    margin-bottom: 0;
}

/* Separator Block */
.wp-block-separator {
    border: none;
    border-top: 1px solid #e1e1e1;
    margin: 3rem auto;
    max-width: 100px;
}

.wp-block-separator.is-style-wide {
    max-width: 100%;
}

.wp-block-separator.is-style-dots {
    border: none;
    text-align: center;
    max-width: none;
    line-height: 1;
    height: auto;
}

.wp-block-separator.is-style-dots::before {
    content: "···";
    color: #666666;
    font-size: 1.5rem;
    letter-spacing: 2rem;
    padding-left: 2rem;
}

/* Spacer Block */
.wp-block-spacer {
    clear: both;
}

/* ==========================================================================
   Widget Blocks
   ========================================================================== */

/* Button Block */
.wp-block-button {
    margin-bottom: 1.5rem;
}

.wp-block-button .wp-block-button__link {
    background-color: #007cba;
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
    transition: background-color 0.3s ease;
}

.wp-block-button .wp-block-button__link:hover {
    background-color: #005a87;
    color: #ffffff;
}

.wp-block-button.is-style-outline .wp-block-button__link {
    background-color: transparent;
    color: #007cba;
    border: 2px solid #007cba;
}

.wp-block-button.is-style-outline .wp-block-button__link:hover {
    background-color: #007cba;
    color: #ffffff;
}

/* List Block */
.wp-block-list {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.wp-block-list li {
    margin-bottom: 0.5rem;
}

/* Table Block */
.wp-block-table {
    margin-bottom: 2rem;
    overflow-x: auto;
}

.wp-block-table table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.wp-block-table th,
.wp-block-table td {
    padding: 0.75rem;
    border: 1px solid #e1e1e1;
    text-align: left;
}

.wp-block-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.wp-block-table.is-style-stripes tbody tr:nth-child(odd) {
    background-color: #f8f9fa;
}

/* Code Block */
.wp-block-code {
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 2rem;
}

.wp-block-code code {
    background: none;
    color: inherit;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    padding: 0;
}

/* Preformatted Block */
.wp-block-preformatted {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 2rem;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
}

/* ==========================================================================
   Embed Blocks
   ========================================================================== */

.wp-block-embed {
    margin-bottom: 2rem;
}

.wp-block-embed iframe,
.wp-block-embed object,
.wp-block-embed video {
    max-width: 100%;
    border-radius: 8px;
}

/* ==========================================================================
   Color Classes
   ========================================================================== */

.has-primary-blue-color {
    color: #007cba;
}

.has-primary-blue-background-color {
    background-color: #007cba;
}

.has-dark-blue-color {
    color: #005a87;
}

.has-dark-blue-background-color {
    background-color: #005a87;
}

.has-light-gray-color {
    color: #f8f9fa;
}

.has-light-gray-background-color {
    background-color: #f8f9fa;
}

.has-dark-gray-color {
    color: #333333;
}

.has-dark-gray-background-color {
    background-color: #333333;
}

/* ==========================================================================
   Responsive Adjustments
   ========================================================================== */

@media (max-width: 767px) {
    .wp-block-columns {
        flex-direction: column;
    }
    
    .wp-block-column {
        flex-basis: auto;
        margin-bottom: 2rem;
    }
    
    .wp-block-cover .wp-block-cover__inner-container {
        padding: 2rem;
    }
    
    .wp-block-pullquote blockquote {
        font-size: 1.5rem;
    }
    
    .alignwide,
    .alignfull {
        margin-left: -1rem;
        margin-right: -1rem;
        width: calc(100% + 2rem);
        max-width: calc(100% + 2rem);
    }
}
