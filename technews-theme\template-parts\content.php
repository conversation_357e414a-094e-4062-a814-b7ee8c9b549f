<?php
/**
 * Template part for displaying posts
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('blog-post'); ?>>
    
    <?php if (has_post_thumbnail()) : ?>
        <div class="post-thumbnail">
            <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                <?php the_post_thumbnail('technews-featured', array(
                    'alt' => the_title_attribute(array('echo' => false)),
                )); ?>
            </a>
        </div>
    <?php endif; ?>

    <div class="post-content">
        
        <header class="entry-header">
            <?php
            // Post categories
            $categories = get_the_category();
            if (!empty($categories)) :
            ?>
                <div class="entry-categories">
                    <?php foreach ($categories as $category) : ?>
                        <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="category-link">
                            <?php echo esc_html($category->name); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php
            if (is_singular()) :
                the_title('<h1 class="entry-title">', '</h1>');
            else :
                the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>');
            endif;
            ?>

            <?php if ('post' === get_post_type()) : ?>
                <div class="entry-meta">
                    <?php technews_posted_on(); ?>
                    <?php technews_posted_by(); ?>
                    
                    <?php if (comments_open() || get_comments_number()) : ?>
                        <span class="comments-link">
                            <?php comments_popup_link(
                                esc_html__('Leave a comment', 'technews-theme'),
                                esc_html__('1 Comment', 'technews-theme'),
                                esc_html__('% Comments', 'technews-theme')
                            ); ?>
                        </span>
                    <?php endif; ?>

                    <!-- Reading time estimate -->
                    <span class="reading-time">
                        <?php echo technews_reading_time(); ?>
                    </span>
                </div>
            <?php endif; ?>
        </header>

        <div class="entry-content">
            <?php
            if (is_singular() || is_home()) {
                the_content(sprintf(
                    wp_kses(
                        __('Continue reading<span class="screen-reader-text"> "%s"</span>', 'technews-theme'),
                        array('span' => array('class' => array()))
                    ),
                    get_the_title()
                ));

                wp_link_pages(array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'technews-theme'),
                    'after'  => '</div>',
                ));
            } else {
                the_excerpt();
            }
            ?>
        </div>

        <footer class="entry-footer">
            <?php technews_entry_footer(); ?>
        </footer>

    </div>

</article><!-- #post-<?php the_ID(); ?> -->
