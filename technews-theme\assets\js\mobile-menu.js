/**
 * Mobile menu functionality
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Back to top button functionality
        var backToTopButton = $('#back-to-top');
        
        if (backToTopButton.length) {
            $(window).scroll(function() {
                if ($(this).scrollTop() > 300) {
                    backToTopButton.addClass('show');
                } else {
                    backToTopButton.removeClass('show');
                }
            });

            backToTopButton.on('click', function(e) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: 0
                }, 600);
            });
        }

        // Sticky header functionality
        var header = $('.site-header');
        var headerHeight = header.outerHeight();
        var stickyClass = 'is-sticky';

        if (header.hasClass('sticky')) {
            $(window).scroll(function() {
                if ($(this).scrollTop() > headerHeight) {
                    header.addClass(stickyClass);
                } else {
                    header.removeClass(stickyClass);
                }
            });
        }

        // Archive view toggle
        $('.view-toggle-btn').on('click', function() {
            var view = $(this).data('view');
            var container = $('.archive-posts-container');
            
            $('.view-toggle-btn').removeClass('active');
            $(this).addClass('active');
            
            container.attr('data-view', view);
        });

        // Archive sort functionality
        $('.archive-sort-select').on('change', function() {
            var sortBy = $(this).val();
            var currentUrl = window.location.href;
            var separator = currentUrl.indexOf('?') !== -1 ? '&' : '?';
            
            window.location.href = currentUrl + separator + 'orderby=' + sortBy;
        });

        // Search form enhancements
        $('.search-form .search-field').on('focus', function() {
            $(this).closest('.search-form').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.search-form').removeClass('focused');
        });

        // Newsletter form handling
        $('.newsletter-form').on('submit', function(e) {
            e.preventDefault();
            
            var form = $(this);
            var email = form.find('input[type="email"]').val();
            
            if (email) {
                // Here you would typically send the email to your newsletter service
                // For now, we'll just show a success message
                alert('Thank you for subscribing to our newsletter!');
                form.find('input[type="email"]').val('');
            }
        });

        // Smooth scrolling for anchor links
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                    return false;
                }
            }
        });

        // Image lazy loading fallback
        if ('loading' in HTMLImageElement.prototype) {
            // Browser supports native lazy loading
            $('img[data-src]').each(function() {
                $(this).attr('src', $(this).data('src')).removeAttr('data-src');
            });
        } else {
            // Fallback for browsers that don't support native lazy loading
            var lazyImages = $('img[data-src]');
            
            if (lazyImages.length && 'IntersectionObserver' in window) {
                var imageObserver = new IntersectionObserver(function(entries, observer) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            var image = entry.target;
                            image.src = image.dataset.src;
                            image.classList.remove('lazy');
                            imageObserver.unobserve(image);
                        }
                    });
                });

                lazyImages.each(function() {
                    imageObserver.observe(this);
                });
            }
        }

        // Breaking news ticker
        var breakingNewsTicker = $('.breaking-news-items');
        if (breakingNewsTicker.length) {
            var items = breakingNewsTicker.find('.breaking-news-item');
            var currentIndex = 0;
            
            if (items.length > 1) {
                setInterval(function() {
                    items.eq(currentIndex).fadeOut(300, function() {
                        currentIndex = (currentIndex + 1) % items.length;
                        items.eq(currentIndex).fadeIn(300);
                    });
                }, 5000);
            }
        }

        // Comment form enhancements
        $('.comment-form input, .comment-form textarea').on('focus', function() {
            $(this).closest('.comment-form').addClass('focused');
        }).on('blur', function() {
            if (!$(this).val()) {
                $(this).closest('.comment-form').removeClass('focused');
            }
        });

        // Social sharing functionality
        $('.social-share-btn').on('click', function(e) {
            var url = $(this).attr('href');
            
            if (url.indexOf('mailto:') === -1) {
                e.preventDefault();
                
                var width = 600;
                var height = 400;
                var left = (screen.width / 2) - (width / 2);
                var top = (screen.height / 2) - (height / 2);
                
                window.open(url, 'share', 'width=' + width + ',height=' + height + ',left=' + left + ',top=' + top + ',scrollbars=yes,resizable=yes');
            }
        });

        // Accessibility improvements
        // Skip link focus fix
        $('.skip-link').on('click', function() {
            var target = $($(this).attr('href'));
            if (target.length) {
                target.attr('tabindex', '-1').focus();
            }
        });

        // Keyboard navigation for dropdowns
        $('.nav-menu a').on('keydown', function(e) {
            var $this = $(this);
            var $parent = $this.parent();
            var $submenu = $parent.find('> .sub-menu');
            
            switch(e.which) {
                case 37: // Left arrow
                    if ($parent.prev().length) {
                        $parent.prev().find('> a').focus();
                    }
                    break;
                case 39: // Right arrow
                    if ($parent.next().length) {
                        $parent.next().find('> a').focus();
                    }
                    break;
                case 40: // Down arrow
                    if ($submenu.length) {
                        e.preventDefault();
                        $submenu.find('a:first').focus();
                    }
                    break;
                case 38: // Up arrow
                    if ($parent.parent().hasClass('sub-menu')) {
                        e.preventDefault();
                        $parent.parent().prev().focus();
                    }
                    break;
            }
        });

    });

})(jQuery);
