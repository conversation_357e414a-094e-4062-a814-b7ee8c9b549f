<?php
/**
 * The template for displaying all pages
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

get_header();
?>

<div class="site-content">
    <div class="container">
        <div class="content-area <?php echo is_active_sidebar('sidebar-1') ? 'has-sidebar' : ''; ?>">
            <main id="main" class="site-main" role="main">

                <?php
                while (have_posts()) :
                    the_post();
                ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class('single-page'); ?>>
                        
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="page-thumbnail">
                                <?php the_post_thumbnail('large', array(
                                    'alt' => the_title_attribute(array('echo' => false)),
                                )); ?>
                            </div>
                        <?php endif; ?>

                        <div class="page-content">
                            
                            <header class="entry-header">
                                <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                            </header>

                            <div class="entry-content">
                                <?php
                                the_content();

                                wp_link_pages(array(
                                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'technews-theme'),
                                    'after'  => '</div>',
                                ));
                                ?>
                            </div>

                            <?php if (get_edit_post_link()) : ?>
                                <footer class="entry-footer">
                                    <?php
                                    edit_post_link(
                                        sprintf(
                                            wp_kses(
                                                __('Edit <span class="screen-reader-text">"%s"</span>', 'technews-theme'),
                                                array('span' => array('class' => array()))
                                            ),
                                            get_the_title()
                                        ),
                                        '<span class="edit-link">',
                                        '</span>'
                                    );
                                    ?>
                                </footer>
                            <?php endif; ?>

                        </div>

                    </article>

                    <?php
                    // Comments
                    if (comments_open() || get_comments_number()) :
                        comments_template();
                    endif;
                    ?>

                <?php endwhile; ?>

            </main>

            <?php if (is_active_sidebar('sidebar-1')) : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<?php get_footer(); ?>
