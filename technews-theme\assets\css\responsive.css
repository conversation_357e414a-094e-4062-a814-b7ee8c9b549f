/*
Theme Name: TechNews Pro
Description: Responsive styles for TechNews Pro theme
Author: WordPress Theme Developer
Version: 1.0.0
*/

/* ==========================================================================
   Mobile First Responsive Design
   ========================================================================== */

/* Base styles are mobile-first (already defined in style.css) */

/* ==========================================================================
   Small Devices (landscape phones, 576px and up)
   ========================================================================== */
@media (min-width: 576px) {
    .container {
        padding: 0 1.5rem;
    }
    
    .single-post .entry-title {
        font-size: 2.75rem;
    }
    
    .featured-post-content {
        padding: 2.5rem;
    }
    
    .grid-post-content {
        padding: 2rem;
    }
}

/* ==========================================================================
   Medium Devices (tablets, 768px and up)
   ========================================================================== */
@media (min-width: 768px) {
    /* Typography adjustments */
    h1 { font-size: 3rem; }
    h2 { font-size: 2.25rem; }
    h3 { font-size: 2rem; }
    
    /* Header improvements */
    .header-container {
        padding: 1.5rem;
    }
    
    .site-title {
        font-size: 1.75rem;
    }
    
    /* Navigation becomes horizontal */
    .main-navigation {
        display: flex;
        align-items: center;
    }
    
    .menu-toggle {
        display: none;
    }
    
    .nav-menu {
        display: flex !important;
        position: static;
        background: none;
        border: none;
        box-shadow: none;
        flex-direction: row;
    }
    
    .nav-menu li {
        border: none;
        margin-left: 2rem;
        margin-bottom: 0;
    }
    
    .nav-menu a {
        padding: 0.5rem 0;
        background: none !important;
    }
    
    /* Content layout */
    .content-area.has-sidebar {
        grid-template-columns: 1fr 300px;
        gap: 3rem;
    }
    
    .content-area.sidebar-left {
        grid-template-columns: 300px 1fr;
    }
    
    /* News grid improvements */
    .news-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2.5rem;
    }
    
    .featured-post {
        grid-template-columns: 1fr 1fr;
        align-items: center;
        gap: 2rem;
    }
    
    /* Single post improvements */
    .single-post-content {
        padding: 3rem;
    }
    
    .single-post .entry-title {
        font-size: 3rem;
    }
    
    /* Author bio improvements */
    .author-bio-content {
        gap: 2rem;
    }
    
    /* Footer improvements */
    .footer-bottom-content {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        text-align: left;
    }
    
    .footer-navigation .footer-nav-menu {
        justify-content: flex-end;
    }
    
    /* Archive improvements */
    .archive-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    /* Search results improvements */
    .search-result {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }
    
    .search-result-thumbnail {
        flex-shrink: 0;
        width: 150px;
    }
    
    .search-result-content {
        flex: 1;
    }
}

/* ==========================================================================
   Large Devices (desktops, 992px and up)
   ========================================================================== */
@media (min-width: 992px) {
    /* Container improvements */
    .container {
        padding: 0 2rem;
    }
    
    /* Header improvements */
    .header-container {
        padding: 2rem;
    }
    
    /* News grid becomes 3 columns */
    .news-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 3rem;
    }
    
    .featured-post {
        grid-column: 1 / -1;
    }
    
    /* Sidebar improvements */
    .content-area.has-sidebar {
        grid-template-columns: 1fr 350px;
        gap: 4rem;
    }
    
    .content-area.sidebar-left {
        grid-template-columns: 350px 1fr;
    }
    
    .widget-area {
        padding: 2.5rem;
    }
    
    /* Single post improvements */
    .single-post-content {
        padding: 4rem;
    }
    
    /* Related posts improvements */
    .related-posts {
        padding: 3rem;
    }
    
    /* Author bio improvements */
    .author-bio {
        padding: 3rem;
    }
    
    /* Footer improvements */
    .footer-widgets {
        padding: 4rem 0;
    }
    
    .footer-bottom {
        padding: 3rem 0;
    }
}

/* ==========================================================================
   Extra Large Devices (large desktops, 1200px and up)
   ========================================================================== */
@media (min-width: 1200px) {
    /* News grid becomes 4 columns */
    .news-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Typography improvements */
    .single-post .entry-title {
        font-size: 3.5rem;
    }
    
    .featured-post .entry-title {
        font-size: 2.5rem;
    }
    
    /* Content improvements */
    .single-post .entry-content {
        font-size: 1.25rem;
        line-height: 1.8;
    }
}

/* ==========================================================================
   Extra Extra Large Devices (1400px and up)
   ========================================================================== */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .header-container {
        max-width: 1400px;
    }
}

/* ==========================================================================
   High DPI / Retina Displays
   ========================================================================== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize images and icons for high DPI displays */
    .custom-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .site-header,
    .site-footer,
    .sidebar,
    .nav-menu,
    .social-sharing,
    .related-posts,
    .comments-area,
    .back-to-top {
        display: none !important;
    }
    
    .site-content {
        padding: 0 !important;
    }
    
    .single-post-content {
        padding: 0 !important;
    }
    
    a[href^="http"]:after {
        content: " (" attr(href) ")";
    }
    
    .entry-title {
        page-break-after: avoid;
    }
    
    .entry-content {
        page-break-inside: avoid;
    }
    
    img {
        max-width: 100% !important;
        page-break-inside: avoid;
    }
    
    p, h2, h3 {
        orphans: 3;
        widows: 3;
    }
    
    h2, h3 {
        page-break-after: avoid;
    }
}

/* ==========================================================================
   Reduced Motion
   ========================================================================== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */
@media (prefers-color-scheme: dark) {
    /* Optional: Add dark mode styles if desired */
    /* This is commented out by default as it's not part of the requirements */
    /*
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .site-header {
        background-color: #2a2a2a;
        border-bottom-color: #444444;
    }
    
    .grid-post,
    .featured-post,
    .single-post,
    .widget-area {
        background-color: #2a2a2a;
        color: #ffffff;
    }
    */
}
