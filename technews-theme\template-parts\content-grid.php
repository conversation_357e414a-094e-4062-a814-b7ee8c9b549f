<?php
/**
 * Template part for displaying posts in grid layout
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('grid-post'); ?>>
    
    <?php if (has_post_thumbnail()) : ?>
        <div class="grid-post-thumbnail">
            <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                <?php the_post_thumbnail('technews-grid', array(
                    'alt' => the_title_attribute(array('echo' => false)),
                )); ?>
            </a>
            
            <!-- Post Category Badge -->
            <?php
            $categories = get_the_category();
            if (!empty($categories)) :
                $primary_category = $categories[0];
            ?>
                <span class="post-category-badge">
                    <a href="<?php echo esc_url(get_category_link($primary_category->term_id)); ?>">
                        <?php echo esc_html($primary_category->name); ?>
                    </a>
                </span>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="grid-post-content">
        
        <header class="entry-header">
            <?php the_title('<h3 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h3>'); ?>

            <div class="entry-meta">
                <span class="posted-on">
                    <?php technews_posted_on(); ?>
                </span>
                
                <?php if (comments_open() || get_comments_number()) : ?>
                    <span class="comments-link">
                        <?php comments_popup_link(
                            '0',
                            '1',
                            '%'
                        ); ?>
                    </span>
                <?php endif; ?>
            </div>
        </header>

        <div class="entry-summary">
            <?php
            // Show excerpt or first 80 words for grid layout
            if (has_excerpt()) {
                echo wp_trim_words(get_the_excerpt(), 15, '...');
            } else {
                echo wp_trim_words(get_the_content(), 15, '...');
            }
            ?>
        </div>

        <footer class="entry-footer">
            <?php if (get_edit_post_link()) : ?>
                <span class="edit-link">
                    <?php edit_post_link(
                        sprintf(
                            wp_kses(
                                __('Edit <span class="screen-reader-text">"%s"</span>', 'technews-theme'),
                                array('span' => array('class' => array()))
                            ),
                            get_the_title()
                        ),
                        '<span class="edit-link">',
                        '</span>'
                    ); ?>
                </span>
            <?php endif; ?>
        </footer>

    </div>

</article><!-- #post-<?php the_ID(); ?> -->
