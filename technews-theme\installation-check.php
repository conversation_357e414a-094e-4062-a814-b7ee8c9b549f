<?php
/**
 * Installation Check Script for TechNews Theme
 * 
 * Upload this file to your WordPress root directory and run it
 * to check if your server meets the theme requirements.
 */

// Check if we're in WordPress
if (!defined('ABSPATH')) {
    // Not in WordPress, do basic checks
    echo "<h1>TechNews Theme Installation Check</h1>";
    
    echo "<h2>Server Requirements</h2>";
    
    // PHP Version Check
    $php_version = PHP_VERSION;
    $php_required = '7.4';
    echo "<p><strong>PHP Version:</strong> $php_version ";
    if (version_compare($php_version, $php_required, '>=')) {
        echo "<span style='color: green;'>✓ OK</span></p>";
    } else {
        echo "<span style='color: red;'>✗ Requires PHP $php_required or higher</span></p>";
    }
    
    // Memory Limit Check
    $memory_limit = ini_get('memory_limit');
    echo "<p><strong>Memory Limit:</strong> $memory_limit ";
    $memory_bytes = wp_convert_hr_to_bytes($memory_limit);
    if ($memory_bytes >= 128 * 1024 * 1024) { // 128MB
        echo "<span style='color: green;'>✓ OK</span></p>";
    } else {
        echo "<span style='color: orange;'>⚠ Recommended: 128M or higher</span></p>";
    }
    
    // File Permissions Check
    echo "<h2>File Permissions</h2>";
    $theme_dir = __DIR__ . '/wp-content/themes/technews-theme';
    if (is_dir($theme_dir)) {
        echo "<p><strong>Theme Directory:</strong> ";
        if (is_readable($theme_dir)) {
            echo "<span style='color: green;'>✓ Readable</span></p>";
        } else {
            echo "<span style='color: red;'>✗ Not readable</span></p>";
        }
        
        $critical_files = ['style.css', 'index.php', 'functions.php'];
        foreach ($critical_files as $file) {
            $file_path = $theme_dir . '/' . $file;
            echo "<p><strong>$file:</strong> ";
            if (file_exists($file_path) && is_readable($file_path)) {
                echo "<span style='color: green;'>✓ OK</span></p>";
            } else {
                echo "<span style='color: red;'>✗ Missing or not readable</span></p>";
            }
        }
    } else {
        echo "<p><span style='color: red;'>✗ Theme directory not found at: $theme_dir</span></p>";
        echo "<p>Make sure you've uploaded the theme to the correct location.</p>";
    }
    
    echo "<h2>Installation Instructions</h2>";
    echo "<ol>";
    echo "<li>Upload the 'technews-theme' folder to '/wp-content/themes/'</li>";
    echo "<li>Go to WordPress Admin → Appearance → Themes</li>";
    echo "<li>Find 'TechNews Pro' and click 'Activate'</li>";
    echo "<li>If you get an error, check the troubleshooting guide</li>";
    echo "</ol>";
    
    exit;
}

// We're in WordPress, do WordPress-specific checks
function technews_installation_check() {
    global $wp_version;
    
    echo "<div style='margin: 20px; padding: 20px; border: 1px solid #ccc;'>";
    echo "<h2>TechNews Theme Installation Check</h2>";
    
    // WordPress Version
    echo "<p><strong>WordPress Version:</strong> $wp_version ";
    if (version_compare($wp_version, '5.0', '>=')) {
        echo "<span style='color: green;'>✓ OK</span></p>";
    } else {
        echo "<span style='color: red;'>✗ Requires WordPress 5.0 or higher</span></p>";
    }
    
    // PHP Version
    $php_version = PHP_VERSION;
    echo "<p><strong>PHP Version:</strong> $php_version ";
    if (version_compare($php_version, '7.4', '>=')) {
        echo "<span style='color: green;'>✓ OK</span></p>";
    } else {
        echo "<span style='color: red;'>✗ Requires PHP 7.4 or higher</span></p>";
    }
    
    // Theme Files Check
    $theme_dir = get_template_directory();
    $critical_files = ['style.css', 'index.php', 'functions.php', 'header.php', 'footer.php'];
    
    echo "<h3>Theme Files</h3>";
    foreach ($critical_files as $file) {
        $file_path = $theme_dir . '/' . $file;
        echo "<p><strong>$file:</strong> ";
        if (file_exists($file_path)) {
            echo "<span style='color: green;'>✓ Found</span></p>";
        } else {
            echo "<span style='color: red;'>✗ Missing</span></p>";
        }
    }
    
    // Active Plugins Check
    $active_plugins = get_option('active_plugins');
    echo "<h3>Active Plugins (" . count($active_plugins) . ")</h3>";
    if (count($active_plugins) > 20) {
        echo "<p><span style='color: orange;'>⚠ You have many plugins active. Consider deactivating some if you experience issues.</span></p>";
    } else {
        echo "<p><span style='color: green;'>✓ Plugin count looks good</span></p>";
    }
    
    echo "<h3>Recommendations</h3>";
    echo "<ul>";
    echo "<li>Create a backup before activating the theme</li>";
    echo "<li>Test the theme on a staging site first</li>";
    echo "<li>If you get errors, check the TROUBLESHOOTING.md file</li>";
    echo "</ul>";
    
    echo "</div>";
}

// Helper function for memory conversion
if (!function_exists('wp_convert_hr_to_bytes')) {
    function wp_convert_hr_to_bytes($size) {
        $size = strtolower($size);
        $bytes = (int) $size;
        
        if (strpos($size, 'k') !== false) {
            $bytes = intval($size) * 1024;
        } elseif (strpos($size, 'm') !== false) {
            $bytes = intval($size) * 1024 * 1024;
        } elseif (strpos($size, 'g') !== false) {
            $bytes = intval($size) * 1024 * 1024 * 1024;
        }
        
        return $bytes;
    }
}

// Run the check if we're in WordPress admin
if (defined('ABSPATH') && is_admin()) {
    add_action('admin_notices', 'technews_installation_check');
}
?>
