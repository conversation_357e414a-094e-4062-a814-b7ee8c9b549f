<?php
/**
 * Template for displaying search forms
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */
?>

<form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
    <label for="search-field-<?php echo uniqid(); ?>" class="screen-reader-text">
        <?php esc_html_e('Search for:', 'technews-theme'); ?>
    </label>
    <div class="search-form-wrapper">
        <input type="search" 
               id="search-field-<?php echo uniqid(); ?>" 
               class="search-field" 
               placeholder="<?php esc_attr_e('Search articles...', 'technews-theme'); ?>" 
               value="<?php echo get_search_query(); ?>" 
               name="s" 
               autocomplete="off" />
        <button type="submit" class="search-submit" aria-label="<?php esc_attr_e('Submit search', 'technews-theme'); ?>">
            <span class="screen-reader-text"><?php esc_html_e('Search', 'technews-theme'); ?></span>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>
        </button>
    </div>
</form>
