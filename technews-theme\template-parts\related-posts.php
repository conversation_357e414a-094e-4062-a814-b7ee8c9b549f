<?php
/**
 * Template part for displaying related posts
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

// Get current post categories
$categories = get_the_category();
if (empty($categories)) {
    return;
}

$category_ids = array();
foreach ($categories as $category) {
    $category_ids[] = $category->term_id;
}

// Query for related posts
$related_posts = new WP_Query(array(
    'category__in'   => $category_ids,
    'post__not_in'   => array(get_the_ID()),
    'posts_per_page' => 3,
    'orderby'        => 'rand',
    'post_status'    => 'publish'
));

if (!$related_posts->have_posts()) {
    return;
}
?>

<section class="related-posts">
    <div class="related-posts-header">
        <h3 class="related-posts-title">
            <?php esc_html_e('Related Articles', 'technews-theme'); ?>
        </h3>
    </div>

    <div class="related-posts-grid">
        <?php
        while ($related_posts->have_posts()) :
            $related_posts->the_post();
        ?>
            <article class="related-post">
                
                <?php if (has_post_thumbnail()) : ?>
                    <div class="related-post-thumbnail">
                        <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                            <?php the_post_thumbnail('technews-grid', array(
                                'alt' => the_title_attribute(array('echo' => false)),
                            )); ?>
                        </a>
                        
                        <!-- Post Category Badge -->
                        <?php
                        $post_categories = get_the_category();
                        if (!empty($post_categories)) :
                            $primary_category = $post_categories[0];
                        ?>
                            <span class="post-category-badge">
                                <a href="<?php echo esc_url(get_category_link($primary_category->term_id)); ?>">
                                    <?php echo esc_html($primary_category->name); ?>
                                </a>
                            </span>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="related-post-content">
                    
                    <header class="entry-header">
                        <?php the_title('<h4 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h4>'); ?>

                        <div class="entry-meta">
                            <span class="posted-on">
                                <?php echo get_the_date(); ?>
                            </span>
                            
                            <span class="reading-time">
                                <?php echo technews_reading_time(); ?>
                            </span>
                        </div>
                    </header>

                    <div class="entry-summary">
                        <?php
                        // Show excerpt or first 80 words for related posts
                        if (has_excerpt()) {
                            echo wp_trim_words(get_the_excerpt(), 12, '...');
                        } else {
                            echo wp_trim_words(get_the_content(), 12, '...');
                        }
                        ?>
                    </div>

                </div>

            </article>
        <?php endwhile; ?>
    </div>

    <?php wp_reset_postdata(); ?>
</section>
