# TechNews Theme Troubleshooting Guide

## Critical Error Solutions

If you're getting a "critical error" when activating the theme, try these solutions in order:

### Solution 1: Use Minimal Functions File
1. Rename `functions.php` to `functions-full.php`
2. Rename `functions-minimal.php` to `functions.php`
3. Try activating the theme again

### Solution 2: Check WordPress Debug Log
1. Add these lines to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```
2. Try activating the theme
3. Check `/wp-content/debug.log` for specific error messages

### Solution 3: Increase Memory Limit
Add this to your `wp-config.php` file:
```php
ini_set('memory_limit', '256M');
```

### Solution 4: Check PHP Version
The theme requires PHP 7.4 or higher. Check your PHP version in:
- WordPress Admin → Tools → Site Health
- Or contact your hosting provider

### Solution 5: Deactivate All Plugins
1. Deactivate all plugins
2. Try activating the theme
3. If it works, reactivate plugins one by one to find conflicts

### Solution 6: Use Default WordPress Theme First
1. Activate a default WordPress theme (Twenty Twenty-Three)
2. Then try activating TechNews theme
3. This helps identify if it's a theme-switching issue

## Common Issues and Fixes

### Issue: "Template parts not found"
**Solution:** The theme includes fallback content, but if you see this error:
1. Make sure all files in `template-parts/` folder are uploaded
2. Check file permissions (should be 644 for files, 755 for folders)

### Issue: "Styles not loading"
**Solution:**
1. Clear any caching plugins
2. Check if `style.css` file exists and is readable
3. Try hard refresh (Ctrl+F5 or Cmd+Shift+R)

### Issue: "Menu not working"
**Solution:**
1. Go to Appearance → Menus
2. Create a new menu
3. Assign it to "Primary Menu" location

### Issue: "Widgets not showing"
**Solution:**
1. Go to Appearance → Widgets
2. Add widgets to "Sidebar" area
3. Check if sidebar is enabled in Customizer

## File Structure Check

Make sure these files exist:
```
technews-theme/
├── style.css ✓
├── index.php ✓
├── functions.php ✓
├── header.php ✓
├── footer.php ✓
├── sidebar.php ✓
├── single.php ✓
├── page.php ✓
├── archive.php ✓
├── search.php ✓
├── 404.php ✓
├── comments.php ✓
└── template-parts/
    ├── content.php ✓
    ├── content-featured.php ✓
    ├── content-grid.php ✓
    ├── author-bio.php ✓
    └── related-posts.php ✓
```

## Emergency Recovery

If the theme completely breaks your site:

### Via FTP/File Manager:
1. Connect to your site via FTP or hosting file manager
2. Navigate to `/wp-content/themes/`
3. Rename `technews-theme` folder to `technews-theme-disabled`
4. WordPress will automatically switch to a default theme

### Via WordPress Admin:
1. Go to Appearance → Themes
2. Activate any other theme
3. Then troubleshoot the TechNews theme

## Getting Help

1. **Check WordPress Site Health:**
   - Go to Tools → Site Health
   - Look for any critical issues

2. **Enable WordPress Debug Mode:**
   - Add debug lines to wp-config.php (see Solution 2 above)
   - Check the debug log for specific errors

3. **Contact Support:**
   - Include your WordPress version
   - Include your PHP version
   - Include any error messages from debug.log
   - Describe exactly when the error occurs

## Performance Tips

1. **Optimize Images:**
   - Use WebP format when possible
   - Compress images before uploading

2. **Use Caching:**
   - Install a caching plugin like WP Rocket or W3 Total Cache

3. **Minimize Plugins:**
   - Only use essential plugins
   - Deactivate unused plugins

## Browser Compatibility

The theme is tested on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

If you experience issues on older browsers, consider using a compatibility plugin.

---

**Remember:** The IDE errors you see (like "Undefined function 'wp_head'") are normal and don't affect the theme's functionality in WordPress. These functions exist in WordPress core.
