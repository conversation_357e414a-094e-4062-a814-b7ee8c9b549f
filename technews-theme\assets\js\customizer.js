/**
 * File customizer.js.
 *
 * Theme Customizer enhancements for a better user experience.
 *
 * Contains handlers to make Theme Customizer preview reload changes asynchronously.
 */

(function($) {
    'use strict';

    // Site title and description.
    wp.customize('blogname', function(value) {
        value.bind(function(to) {
            $('.site-title a').text(to);
        });
    });
    
    wp.customize('blogdescription', function(value) {
        value.bind(function(to) {
            $('.site-description').text(to);
        });
    });

    // Header text color.
    wp.customize('header_textcolor', function(value) {
        value.bind(function(to) {
            if ('blank' === to) {
                $('.site-title, .site-description').css({
                    'clip': 'rect(1px, 1px, 1px, 1px)',
                    'position': 'absolute'
                });
            } else {
                $('.site-title, .site-description').css({
                    'clip': 'auto',
                    'position': 'relative'
                });
                $('.site-title a, .site-description').css({
                    'color': to
                });
            }
        });
    });

    // Primary color
    wp.customize('primary_color', function(value) {
        value.bind(function(to) {
            var style = '<style id="technews-primary-color">' +
                ':root { --primary-color: ' + to + '; }' +
                'a, .category-link:hover, .tag-cloud-link:hover, ' +
                '.btn, .post-category-badge, .widget-title, ' +
                '.footer-widget-area .widget-title, .social-share-btn.facebook, ' +
                '.back-to-top, .author-posts-link { background-color: ' + to + '; }' +
                '.entry-meta a:hover, .grid-post .entry-title a:hover, ' +
                '.widget a:hover, .footer-widget-area a:hover, ' +
                '.footer-navigation a:hover, .author-name a:hover { color: ' + to + '; }' +
                '</style>';
            
            $('#technews-primary-color').remove();
            $('head').append(style);
        });
    });

    // Secondary color
    wp.customize('secondary_color', function(value) {
        value.bind(function(to) {
            var style = '<style id="technews-secondary-color">' +
                ':root { --secondary-color: ' + to + '; }' +
                'a:hover, .btn:hover, .back-to-top:hover, ' +
                '.social-navigation a:hover, .author-social a:hover { background-color: ' + to + '; }' +
                '</style>';
            
            $('#technews-secondary-color').remove();
            $('head').append(style);
        });
    });

    // Sticky header
    wp.customize('sticky_header', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.site-header').addClass('sticky');
                $('body').addClass('has-sticky-header');
            } else {
                $('.site-header').removeClass('sticky');
                $('body').removeClass('has-sticky-header');
            }
        });
    });

    // Sidebar position
    wp.customize('sidebar_position', function(value) {
        value.bind(function(to) {
            $('body').removeClass('sidebar-left sidebar-right');
            $('body').addClass('sidebar-' + to);
            
            $('.content-area').removeClass('sidebar-left');
            if (to === 'left') {
                $('.content-area').addClass('sidebar-left');
            }
        });
    });

    // Show author bio
    wp.customize('show_author_bio', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.author-bio').show();
            } else {
                $('.author-bio').hide();
            }
        });
    });

    // Show related posts
    wp.customize('show_related_posts', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.related-posts').show();
            } else {
                $('.related-posts').hide();
            }
        });
    });

    // Show social sharing
    wp.customize('show_social_sharing', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.social-sharing').show();
            } else {
                $('.social-sharing').hide();
            }
        });
    });

    // Copyright text
    wp.customize('copyright_text', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.site-info p:first-child').html(to);
            } else {
                var defaultText = '&copy; ' + new Date().getFullYear() + ' ' +
                    '<a href="' + window.location.origin + '">' + 
                    wp.customize('blogname')() + '</a> All rights reserved.';
                $('.site-info p:first-child').html(defaultText);
            }
        });
    });

    // Show theme credit
    wp.customize('show_theme_credit', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.theme-credit').show();
            } else {
                $('.theme-credit').hide();
            }
        });
    });

    // Show back to top button
    wp.customize('show_back_to_top', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.back-to-top').show();
            } else {
                $('.back-to-top').hide();
            }
        });
    });

    // Breaking news ticker
    wp.customize('enable_breaking_news', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.breaking-news-ticker').show();
            } else {
                $('.breaking-news-ticker').hide();
            }
        });
    });

    // Newsletter widget
    wp.customize('show_newsletter_widget', function(value) {
        value.bind(function(to) {
            if (to) {
                $('.widget_newsletter').show();
            } else {
                $('.widget_newsletter').hide();
            }
        });
    });

    // Newsletter description
    wp.customize('newsletter_description', function(value) {
        value.bind(function(to) {
            $('.newsletter-content p').text(to);
        });
    });

})(jQuery);
