<?php
/**
 * The template for displaying all single posts
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

get_header();
?>

<div class="site-content">
    <div class="container">
        <div class="content-area <?php echo is_active_sidebar('sidebar-1') ? 'has-sidebar' : ''; ?>">
            <main id="main" class="site-main" role="main">

                <?php
                while (have_posts()) :
                    the_post();
                ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?>>
                        
                        <!-- Featured Image -->
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="single-post-thumbnail">
                                <?php the_post_thumbnail('large', array(
                                    'alt' => the_title_attribute(array('echo' => false)),
                                )); ?>
                                
                                <?php
                                // Image caption
                                $thumbnail_id = get_post_thumbnail_id();
                                $caption = wp_get_attachment_caption($thumbnail_id);
                                if ($caption) :
                                ?>
                                    <figcaption class="wp-caption-text"><?php echo esc_html($caption); ?></figcaption>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="single-post-content">
                            
                            <header class="entry-header">
                                <?php
                                // Post categories
                                $categories = get_the_category();
                                if (!empty($categories)) :
                                ?>
                                    <div class="entry-categories">
                                        <?php foreach ($categories as $category) : ?>
                                            <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="category-link">
                                                <?php echo esc_html($category->name); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <?php the_title('<h1 class="entry-title">', '</h1>'); ?>

                                <div class="entry-meta">
                                    <?php technews_posted_on(); ?>
                                    <?php technews_posted_by(); ?>
                                    
                                    <?php if (comments_open() || get_comments_number()) : ?>
                                        <span class="comments-link">
                                            <?php comments_popup_link(
                                                esc_html__('Leave a comment', 'technews-theme'),
                                                esc_html__('1 Comment', 'technews-theme'),
                                                esc_html__('% Comments', 'technews-theme')
                                            ); ?>
                                        </span>
                                    <?php endif; ?>

                                    <!-- Reading time estimate -->
                                    <span class="reading-time">
                                        <?php echo technews_reading_time(); ?>
                                    </span>

                                    <!-- Social sharing buttons -->
                                    <?php if (get_theme_mod('show_social_sharing', true)) : ?>
                                        <div class="social-sharing">
                                            <?php technews_social_sharing_buttons(); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </header>

                            <div class="entry-content">
                                <?php
                                the_content();

                                wp_link_pages(array(
                                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'technews-theme'),
                                    'after'  => '</div>',
                                ));
                                ?>
                            </div>

                            <footer class="entry-footer">
                                <?php technews_entry_footer(); ?>
                            </footer>

                        </div>

                    </article>

                    <?php
                    // Author bio box
                    if (get_theme_mod('show_author_bio', true)) :
                        get_template_part('template-parts/author-bio');
                    endif;
                    ?>

                    <?php
                    // Related posts
                    if (get_theme_mod('show_related_posts', true)) :
                        get_template_part('template-parts/related-posts');
                    endif;
                    ?>

                    <?php
                    // Post navigation
                    the_post_navigation(array(
                        'prev_text' => '<span class="nav-subtitle">' . esc_html__('Previous:', 'technews-theme') . '</span> <span class="nav-title">%title</span>',
                        'next_text' => '<span class="nav-subtitle">' . esc_html__('Next:', 'technews-theme') . '</span> <span class="nav-title">%title</span>',
                    ));
                    ?>

                    <?php
                    // Comments
                    if (comments_open() || get_comments_number()) :
                        comments_template();
                    endif;
                    ?>

                <?php endwhile; ?>

            </main>

            <?php if (is_active_sidebar('sidebar-1')) : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<?php get_footer(); ?>
