<?php
/**
 * Functions which enhance the theme by hooking into WordPress
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

/**
 * Adds custom classes to the array of body classes.
 */
function technews_body_classes($classes) {
    // Adds a class of hfeed to non-singular pages.
    if (!is_singular()) {
        $classes[] = 'hfeed';
    }

    // Adds a class of no-sidebar when there is no sidebar present.
    if (!is_active_sidebar('sidebar-1')) {
        $classes[] = 'no-sidebar';
    }

    // Add class for sticky header
    if (get_theme_mod('sticky_header', true)) {
        $classes[] = 'has-sticky-header';
    }

    // Add class for sidebar position
    $sidebar_position = get_theme_mod('sidebar_position', 'right');
    if (is_active_sidebar('sidebar-1')) {
        $classes[] = 'has-sidebar';
        $classes[] = 'sidebar-' . $sidebar_position;
    }

    return $classes;
}
add_filter('body_class', 'technews_body_classes');

/**
 * Add a pingback url auto-discovery header for single posts, pages, or attachments.
 */
function technews_pingback_header() {
    if (is_singular() && pings_open()) {
        printf('<link rel="pingback" href="%s">', esc_url(get_bloginfo('pingback_url')));
    }
}
add_action('wp_head', 'technews_pingback_header');

/**
 * Customize excerpt length
 */
function technews_excerpt_length($length) {
    if (is_admin()) {
        return $length;
    }
    
    return 25;
}
add_filter('excerpt_length', 'technews_excerpt_length');

/**
 * Customize excerpt more string
 */
function technews_excerpt_more($more) {
    if (is_admin()) {
        return $more;
    }
    
    return '...';
}
add_filter('excerpt_more', 'technews_excerpt_more');

/**
 * Add custom search form
 */
function technews_search_form($form) {
    $form = '<form role="search" method="get" class="search-form" action="' . esc_url(home_url('/')) . '">
        <label for="search-field" class="screen-reader-text">' . esc_html__('Search for:', 'technews-theme') . '</label>
        <input type="search" id="search-field" class="search-field" placeholder="' . esc_attr__('Search...', 'technews-theme') . '" value="' . get_search_query() . '" name="s" />
        <button type="submit" class="search-submit">
            <span class="screen-reader-text">' . esc_html__('Search', 'technews-theme') . '</span>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
            </svg>
        </button>
    </form>';
    
    return $form;
}
add_filter('get_search_form', 'technews_search_form');

/**
 * Enqueue comment reply script
 */
function technews_comment_reply() {
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'technews_comment_reply');

/**
 * Custom comment form
 */
function technews_comment_form_defaults($defaults) {
    $defaults['comment_notes_before'] = '';
    $defaults['comment_notes_after'] = '';
    $defaults['id_form'] = 'commentform';
    $defaults['id_submit'] = 'submit';
    $defaults['class_submit'] = 'btn btn-primary';
    $defaults['name_submit'] = 'submit';
    $defaults['title_reply'] = esc_html__('Leave a Comment', 'technews-theme');
    $defaults['title_reply_to'] = esc_html__('Leave a Reply to %s', 'technews-theme');
    $defaults['cancel_reply_link'] = esc_html__('Cancel Reply', 'technews-theme');
    $defaults['label_submit'] = esc_html__('Post Comment', 'technews-theme');
    $defaults['submit_button'] = '<input name="%1$s" type="submit" id="%2$s" class="%3$s" value="%4$s" />';
    $defaults['submit_field'] = '<p class="form-submit">%1$s %2$s</p>';
    
    return $defaults;
}
add_filter('comment_form_defaults', 'technews_comment_form_defaults');

/**
 * Custom comment form fields
 */
function technews_comment_form_fields($fields) {
    $commenter = wp_get_current_commenter();
    $req = get_option('require_name_email');
    $aria_req = ($req ? " aria-required='true'" : '');
    
    $fields['author'] = '<p class="comment-form-author">
        <label for="author">' . esc_html__('Name', 'technews-theme') . ($req ? ' <span class="required">*</span>' : '') . '</label>
        <input id="author" name="author" type="text" value="' . esc_attr($commenter['comment_author']) . '" size="30"' . $aria_req . ' />
    </p>';
    
    $fields['email'] = '<p class="comment-form-email">
        <label for="email">' . esc_html__('Email', 'technews-theme') . ($req ? ' <span class="required">*</span>' : '') . '</label>
        <input id="email" name="email" type="email" value="' . esc_attr($commenter['comment_author_email']) . '" size="30"' . $aria_req . ' />
    </p>';
    
    $fields['url'] = '<p class="comment-form-url">
        <label for="url">' . esc_html__('Website', 'technews-theme') . '</label>
        <input id="url" name="url" type="url" value="' . esc_attr($commenter['comment_author_url']) . '" size="30" />
    </p>';
    
    return $fields;
}
add_filter('comment_form_default_fields', 'technews_comment_form_fields');

/**
 * Add post views counter
 */
function technews_set_post_views($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if ($count == '') {
        $count = 0;
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
    } else {
        $count++;
        update_post_meta($postID, $count_key, $count);
    }
}

function technews_track_post_views($post_id) {
    if (!is_single()) return;
    if (empty($post_id)) {
        global $post;
        $post_id = $post->ID;
    }
    technews_set_post_views($post_id);
}
add_action('wp_head', 'technews_track_post_views');

/**
 * Get post views count
 */
function technews_get_post_views($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if ($count == '') {
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0 View";
    }
    return $count . ' Views';
}

/**
 * Custom archive title
 */
function technews_archive_title($title) {
    if (is_category()) {
        $title = single_cat_title('', false);
    } elseif (is_tag()) {
        $title = single_tag_title('', false);
    } elseif (is_author()) {
        $title = sprintf(esc_html__('Author: %s', 'technews-theme'), '<span class="vcard">' . get_the_author() . '</span>');
    } elseif (is_year()) {
        $title = sprintf(esc_html__('Year: %s', 'technews-theme'), get_the_date('Y'));
    } elseif (is_month()) {
        $title = sprintf(esc_html__('Month: %s', 'technews-theme'), get_the_date('F Y'));
    } elseif (is_day()) {
        $title = sprintf(esc_html__('Day: %s', 'technews-theme'), get_the_date('F j, Y'));
    } elseif (is_tax('post_format')) {
        if (is_tax('post_format', 'post-format-aside')) {
            $title = esc_html_x('Asides', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-gallery')) {
            $title = esc_html_x('Galleries', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-link')) {
            $title = esc_html_x('Links', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-image')) {
            $title = esc_html_x('Images', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-quote')) {
            $title = esc_html_x('Quotes', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-status')) {
            $title = esc_html_x('Statuses', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-video')) {
            $title = esc_html_x('Videos', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-audio')) {
            $title = esc_html_x('Audios', 'post format archive title', 'technews-theme');
        } elseif (is_tax('post_format', 'post-format-chat')) {
            $title = esc_html_x('Chats', 'post format archive title', 'technews-theme');
        }
    } elseif (is_post_type_archive()) {
        $title = sprintf(esc_html__('Archives: %s', 'technews-theme'), post_type_archive_title('', false));
    } elseif (is_tax()) {
        $tax = get_taxonomy(get_queried_object()->taxonomy);
        $title = sprintf(esc_html__('%1$s: %2$s', 'technews-theme'), $tax->labels->singular_name, single_term_title('', false));
    }

    return $title;
}
add_filter('get_the_archive_title', 'technews_archive_title');

/**
 * Add theme support for Gutenberg wide images in the editor
 */
function technews_gutenberg_css() {
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');
}
add_action('after_setup_theme', 'technews_gutenberg_css');

/**
 * Enqueue Gutenberg block styles for front-end
 */
function technews_block_styles() {
    wp_enqueue_style('technews-block-style', get_template_directory_uri() . '/assets/css/blocks.css', array(), TECHNEWS_VERSION);
}
add_action('wp_enqueue_scripts', 'technews_block_styles');

/**
 * Add custom image sizes to media library
 */
function technews_custom_image_sizes($sizes) {
    return array_merge($sizes, array(
        'technews-featured' => esc_html__('Featured Image', 'technews-theme'),
        'technews-grid' => esc_html__('Grid Image', 'technews-theme'),
        'technews-small' => esc_html__('Small Image', 'technews-theme'),
    ));
}
add_filter('image_size_names_choose', 'technews_custom_image_sizes');
