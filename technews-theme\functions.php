<?php
/**
 * TechNews Theme functions and definitions
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme version
 */
define('TECHNEWS_VERSION', '1.0.0');

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function technews_setup() {
    // Make theme available for translation
    load_theme_textdomain('technews-theme', get_template_directory() . '/languages');

    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Set default thumbnail sizes
    set_post_thumbnail_size(1200, 675, true); // 16:9 ratio
    add_image_size('technews-featured', 800, 450, true);
    add_image_size('technews-grid', 400, 225, true);
    add_image_size('technews-small', 150, 150, true);

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'technews-theme'),
        'footer'  => esc_html__('Footer Menu', 'technews-theme'),
        'social'  => esc_html__('Social Links Menu', 'technews-theme'),
    ));

    // Switch default core markup to output valid HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add theme support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 400,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ));

    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-image'      => '',
        'default-text-color' => '000000',
        'width'              => 1200,
        'height'             => 300,
        'flex-width'         => true,
        'flex-height'        => true,
    ));

    // Add support for editor styles
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');

    // Add support for responsive embeds
    add_theme_support('responsive-embeds');

    // Add support for wide and full alignment
    add_theme_support('align-wide');

    // Add support for block styles
    add_theme_support('wp-block-styles');

    // Add support for editor color palette
    add_theme_support('editor-color-palette', array(
        array(
            'name'  => esc_html__('Primary Blue', 'technews-theme'),
            'slug'  => 'primary-blue',
            'color' => '#007cba',
        ),
        array(
            'name'  => esc_html__('Dark Blue', 'technews-theme'),
            'slug'  => 'dark-blue',
            'color' => '#005a87',
        ),
        array(
            'name'  => esc_html__('Light Gray', 'technews-theme'),
            'slug'  => 'light-gray',
            'color' => '#f8f9fa',
        ),
        array(
            'name'  => esc_html__('Dark Gray', 'technews-theme'),
            'slug'  => 'dark-gray',
            'color' => '#333333',
        ),
    ));

    // Add support for custom line height
    add_theme_support('custom-line-height');

    // Add support for custom units
    add_theme_support('custom-units');

    // Add support for custom spacing
    add_theme_support('custom-spacing');
}
add_action('after_setup_theme', 'technews_setup');

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 */
function technews_content_width() {
    $GLOBALS['content_width'] = apply_filters('technews_content_width', 1200);
}
add_action('after_setup_theme', 'technews_content_width', 0);

/**
 * Register widget areas
 */
function technews_widgets_init() {
    // Main sidebar
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'technews-theme'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here to appear in your sidebar.', 'technews-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    // Footer widget areas
    for ($i = 1; $i <= 4; $i++) {
        register_sidebar(array(
            'name'          => sprintf(esc_html__('Footer Widget Area %d', 'technews-theme'), $i),
            'id'            => 'footer-' . $i,
            'description'   => sprintf(esc_html__('Add widgets here to appear in footer column %d.', 'technews-theme'), $i),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget'  => '</section>',
            'before_title'  => '<h4 class="widget-title">',
            'after_title'   => '</h4>',
        ));
    }
}
add_action('widgets_init', 'technews_widgets_init');

/**
 * Enqueue scripts and styles
 */
function technews_scripts() {
    // Main stylesheet
    wp_enqueue_style('technews-style', get_stylesheet_uri(), array(), TECHNEWS_VERSION);

    // RTL stylesheet
    wp_style_add_data('technews-style', 'rtl', 'replace');

    // Custom CSS for responsive design
    wp_enqueue_style('technews-responsive', get_template_directory_uri() . '/assets/css/responsive.css', array('technews-style'), TECHNEWS_VERSION);

    // Main JavaScript
    wp_enqueue_script('technews-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array(), TECHNEWS_VERSION, true);

    // Responsive navigation script
    wp_enqueue_script('technews-mobile-menu', get_template_directory_uri() . '/assets/js/mobile-menu.js', array('jquery'), TECHNEWS_VERSION, true);

    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }

    // Skip link focus fix for IE11
    wp_enqueue_script('technews-skip-link-focus-fix', get_template_directory_uri() . '/assets/js/skip-link-focus-fix.js', array(), TECHNEWS_VERSION, true);
}
add_action('wp_enqueue_scripts', 'technews_scripts');

/**
 * Enqueue block editor styles
 */
function technews_block_editor_styles() {
    wp_enqueue_style('technews-block-editor-style', get_template_directory_uri() . '/assets/css/editor-style.css', array(), TECHNEWS_VERSION);
}
add_action('enqueue_block_editor_assets', 'technews_block_editor_styles');

/**
 * Custom template tags for this theme
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file
 */
if (defined('JETPACK__VERSION')) {
    require get_template_directory() . '/inc/jetpack.php';
}

/**
 * Add custom classes to body
 */
function technews_body_classes($classes) {
    // Add class for sidebar position
    $sidebar_position = get_theme_mod('sidebar_position', 'right');
    if (is_active_sidebar('sidebar-1')) {
        $classes[] = 'has-sidebar';
        $classes[] = 'sidebar-' . $sidebar_position;
    }

    // Add class for sticky header
    if (get_theme_mod('sticky_header', true)) {
        $classes[] = 'has-sticky-header';
    }

    return $classes;
}
add_filter('body_class', 'technews_body_classes');

/**
 * Add preconnect for Google Fonts
 */
function technews_resource_hints($urls, $relation_type) {
    if (wp_style_is('technews-fonts', 'queue') && 'preconnect' === $relation_type) {
        $urls[] = array(
            'href' => 'https://fonts.gstatic.com',
            'crossorigin',
        );
    }

    return $urls;
}
add_filter('wp_resource_hints', 'technews_resource_hints', 10, 2);

/**
 * Implement the Custom Header feature
 */
require get_template_directory() . '/inc/custom-header.php';
