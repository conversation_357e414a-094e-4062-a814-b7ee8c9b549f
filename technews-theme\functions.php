<?php

/**
 * TechNews Theme functions and definitions
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check WordPress version
if (version_compare($GLOBALS['wp_version'], '5.0', '<')) {
    add_action('admin_notices', 'technews_wordpress_version_notice');
    return;
}

// Check PHP version
if (version_compare(PHP_VERSION, '7.4', '<')) {
    add_action('admin_notices', 'technews_php_version_notice');
    return;
}

/**
 * Display WordPress version notice
 */
function technews_wordpress_version_notice()
{
    echo '<div class="notice notice-error"><p>';
    echo esc_html__('TechNews Theme requires WordPress 5.0 or higher. Please update WordPress.', 'technews-theme');
    echo '</p></div>';
}

/**
 * Display PHP version notice
 */
function technews_php_version_notice()
{
    echo '<div class="notice notice-error"><p>';
    echo esc_html__('TechNews Theme requires PHP 7.4 or higher. Please update PHP.', 'technews-theme');
    echo '</p></div>';
}

/**
 * Theme version
 */
define('TECHNEWS_VERSION', '1.0.0');

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function technews_setup()
{
    // Make theme available for translation
    load_theme_textdomain('technews-theme', get_template_directory() . '/languages');

    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Set default thumbnail sizes
    set_post_thumbnail_size(1200, 675, true); // 16:9 ratio
    add_image_size('technews-featured', 800, 450, true);
    add_image_size('technews-grid', 400, 225, true);
    add_image_size('technews-small', 150, 150, true);

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'technews-theme'),
        'footer'  => esc_html__('Footer Menu', 'technews-theme'),
        'social'  => esc_html__('Social Links Menu', 'technews-theme'),
    ));

    // Switch default core markup to output valid HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add theme support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 400,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ));

    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-image'      => '',
        'default-text-color' => '000000',
        'width'              => 1200,
        'height'             => 300,
        'flex-width'         => true,
        'flex-height'        => true,
    ));

    // Add support for editor styles
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');

    // Add support for responsive embeds
    add_theme_support('responsive-embeds');

    // Add support for wide and full alignment
    add_theme_support('align-wide');

    // Add support for block styles
    add_theme_support('wp-block-styles');

    // Add support for editor color palette
    add_theme_support('editor-color-palette', array(
        array(
            'name'  => esc_html__('Primary Blue', 'technews-theme'),
            'slug'  => 'primary-blue',
            'color' => '#007cba',
        ),
        array(
            'name'  => esc_html__('Dark Blue', 'technews-theme'),
            'slug'  => 'dark-blue',
            'color' => '#005a87',
        ),
        array(
            'name'  => esc_html__('Light Gray', 'technews-theme'),
            'slug'  => 'light-gray',
            'color' => '#f8f9fa',
        ),
        array(
            'name'  => esc_html__('Dark Gray', 'technews-theme'),
            'slug'  => 'dark-gray',
            'color' => '#333333',
        ),
    ));

    // Add support for custom line height
    add_theme_support('custom-line-height');

    // Add support for custom units
    add_theme_support('custom-units');

    // Add support for custom spacing
    add_theme_support('custom-spacing');
}
add_action('after_setup_theme', 'technews_setup');

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 */
function technews_content_width()
{
    $GLOBALS['content_width'] = apply_filters('technews_content_width', 1200);
}
add_action('after_setup_theme', 'technews_content_width', 0);

/**
 * Register widget areas
 */
function technews_widgets_init()
{
    // Main sidebar
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'technews-theme'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here to appear in your sidebar.', 'technews-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    // Footer widget areas
    for ($i = 1; $i <= 4; $i++) {
        register_sidebar(array(
            'name'          => sprintf(esc_html__('Footer Widget Area %d', 'technews-theme'), $i),
            'id'            => 'footer-' . $i,
            'description'   => sprintf(esc_html__('Add widgets here to appear in footer column %d.', 'technews-theme'), $i),
            'before_widget' => '<section id="%1$s" class="widget %2$s">',
            'after_widget'  => '</section>',
            'before_title'  => '<h4 class="widget-title">',
            'after_title'   => '</h4>',
        ));
    }
}
add_action('widgets_init', 'technews_widgets_init');

/**
 * Enqueue scripts and styles
 */
function technews_scripts()
{
    // Main stylesheet
    wp_enqueue_style('technews-style', get_stylesheet_uri(), array(), TECHNEWS_VERSION);

    // RTL stylesheet
    wp_style_add_data('technews-style', 'rtl', 'replace');

    // Custom CSS for responsive design
    wp_enqueue_style('technews-responsive', get_template_directory_uri() . '/assets/css/responsive.css', array('technews-style'), TECHNEWS_VERSION);

    // Main JavaScript
    wp_enqueue_script('technews-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array(), TECHNEWS_VERSION, true);

    // Responsive navigation script
    wp_enqueue_script('technews-mobile-menu', get_template_directory_uri() . '/assets/js/mobile-menu.js', array('jquery'), TECHNEWS_VERSION, true);

    // Comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }

    // Skip link focus fix for IE11
    wp_enqueue_script('technews-skip-link-focus-fix', get_template_directory_uri() . '/assets/js/skip-link-focus-fix.js', array(), TECHNEWS_VERSION, true);
}
add_action('wp_enqueue_scripts', 'technews_scripts');

/**
 * Enqueue block editor styles
 */
function technews_block_editor_styles()
{
    wp_enqueue_style('technews-block-editor-style', get_template_directory_uri() . '/assets/css/editor-style.css', array(), TECHNEWS_VERSION);
}
add_action('enqueue_block_editor_assets', 'technews_block_editor_styles');

/**
 * Custom template tags for this theme
 */
if (file_exists(get_template_directory() . '/inc/template-tags.php')) {
    require get_template_directory() . '/inc/template-tags.php';
}

/**
 * Functions which enhance the theme by hooking into WordPress
 */
if (file_exists(get_template_directory() . '/inc/template-functions.php')) {
    require get_template_directory() . '/inc/template-functions.php';
}

/**
 * Customizer additions
 */
if (file_exists(get_template_directory() . '/inc/customizer.php')) {
    require get_template_directory() . '/inc/customizer.php';
}

/**
 * Load Jetpack compatibility file
 */
if (defined('JETPACK__VERSION') && file_exists(get_template_directory() . '/inc/jetpack.php')) {
    require get_template_directory() . '/inc/jetpack.php';
}

/**
 * Add custom classes to body
 */
function technews_body_classes($classes)
{
    // Add class for sidebar position
    $sidebar_position = get_theme_mod('sidebar_position', 'right');
    if (is_active_sidebar('sidebar-1')) {
        $classes[] = 'has-sidebar';
        $classes[] = 'sidebar-' . $sidebar_position;
    }

    // Add class for sticky header
    if (get_theme_mod('sticky_header', true)) {
        $classes[] = 'has-sticky-header';
    }

    return $classes;
}
add_filter('body_class', 'technews_body_classes');

/**
 * Add preconnect for Google Fonts
 */
function technews_resource_hints($urls, $relation_type)
{
    if (wp_style_is('technews-fonts', 'queue') && 'preconnect' === $relation_type) {
        $urls[] = array(
            'href' => 'https://fonts.gstatic.com',
            'crossorigin',
        );
    }

    return $urls;
}
add_filter('wp_resource_hints', 'technews_resource_hints', 10, 2);

/**
 * Implement the Custom Header feature
 */
if (file_exists(get_template_directory() . '/inc/custom-header.php')) {
    require get_template_directory() . '/inc/custom-header.php';
}

/**
 * Essential template functions (fallbacks if inc files don't exist)
 */
if (!function_exists('technews_posted_on')) {
    function technews_posted_on()
    {
        $time_string = '<time class="entry-date published updated" datetime="%1$s">%2$s</time>';
        if (get_the_time('U') !== get_the_modified_time('U')) {
            $time_string = '<time class="entry-date published" datetime="%1$s">%2$s</time><time class="updated" datetime="%3$s">%4$s</time>';
        }

        $time_string = sprintf(
            $time_string,
            esc_attr(get_the_date(DATE_W3C)),
            esc_html(get_the_date()),
            esc_attr(get_the_modified_date(DATE_W3C)),
            esc_html(get_the_modified_date())
        );

        $posted_on = sprintf(
            esc_html_x('Posted on %s', 'post date', 'technews-theme'),
            '<a href="' . esc_url(get_permalink()) . '" rel="bookmark">' . $time_string . '</a>'
        );

        echo '<span class="posted-on">' . $posted_on . '</span>';
    }
}

if (!function_exists('technews_posted_by')) {
    function technews_posted_by()
    {
        $byline = sprintf(
            esc_html_x('by %s', 'post author', 'technews-theme'),
            '<span class="author vcard"><a class="url fn n" href="' . esc_url(get_author_posts_url(get_the_author_meta('ID'))) . '">' . esc_html(get_the_author()) . '</a></span>'
        );

        echo '<span class="byline"> ' . $byline . '</span>';
    }
}

if (!function_exists('technews_entry_footer')) {
    function technews_entry_footer()
    {
        if ('post' === get_post_type()) {
            $categories_list = get_the_category_list(esc_html__(', ', 'technews-theme'));
            if ($categories_list) {
                printf('<span class="cat-links">' . esc_html__('Posted in %1$s', 'technews-theme') . '</span>', $categories_list);
            }

            $tags_list = get_the_tag_list('', esc_html_x(', ', 'list item separator', 'technews-theme'));
            if ($tags_list) {
                printf('<span class="tags-links">' . esc_html__('Tagged %1$s', 'technews-theme') . '</span>', $tags_list);
            }
        }

        edit_post_link(
            sprintf(
                wp_kses(
                    __('Edit <span class="screen-reader-text">"%s"</span>', 'technews-theme'),
                    array('span' => array('class' => array()))
                ),
                get_the_title()
            ),
            '<span class="edit-link">',
            '</span>'
        );
    }
}

if (!function_exists('technews_reading_time')) {
    function technews_reading_time()
    {
        $content = get_post_field('post_content', get_the_ID());
        $word_count = str_word_count(strip_tags($content));
        $reading_time = ceil($word_count / 200);

        if ($reading_time == 1) {
            return sprintf(esc_html__('%d min read', 'technews-theme'), $reading_time);
        } else {
            return sprintf(esc_html__('%d mins read', 'technews-theme'), $reading_time);
        }
    }
}

if (!function_exists('technews_social_sharing_buttons')) {
    function technews_social_sharing_buttons()
    {
        $post_url = urlencode(get_permalink());
        $post_title = urlencode(get_the_title());

        echo '<div class="social-sharing-buttons">';
        echo '<a href="https://www.facebook.com/sharer/sharer.php?u=' . $post_url . '" target="_blank" class="social-share-btn facebook">Facebook</a>';
        echo '<a href="https://twitter.com/intent/tweet?url=' . $post_url . '&text=' . $post_title . '" target="_blank" class="social-share-btn twitter">Twitter</a>';
        echo '<a href="https://www.linkedin.com/sharing/share-offsite/?url=' . $post_url . '" target="_blank" class="social-share-btn linkedin">LinkedIn</a>';
        echo '</div>';
    }
}

if (!function_exists('technews_breadcrumbs')) {
    function technews_breadcrumbs()
    {
        if (is_front_page()) {
            return;
        }

        $separator = ' / ';
        $home_title = esc_html__('Home', 'technews-theme');

        echo '<nav class="breadcrumbs">';
        echo '<a href="' . esc_url(home_url('/')) . '">' . $home_title . '</a>';

        if (is_category() || is_single()) {
            echo $separator;
            the_category(', ');
            if (is_single()) {
                echo $separator;
                echo '<span class="current">' . get_the_title() . '</span>';
            }
        } elseif (is_page()) {
            echo $separator;
            echo '<span class="current">' . get_the_title() . '</span>';
        }

        echo '</nav>';
    }
}

if (!function_exists('technews_default_menu')) {
    function technews_default_menu()
    {
        echo '<ul id="primary-menu" class="nav-menu">';
        echo '<li><a href="' . esc_url(home_url('/')) . '">' . esc_html__('Home', 'technews-theme') . '</a></li>';

        // Get some categories to show
        $categories = get_categories(array('number' => 5));
        if ($categories) {
            foreach ($categories as $category) {
                echo '<li><a href="' . esc_url(get_category_link($category->term_id)) . '">' . esc_html($category->name) . '</a></li>';
            }
        }

        echo '</ul>';
    }
}
