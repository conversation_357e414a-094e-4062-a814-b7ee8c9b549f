<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#main"><?php esc_html_e('Skip to content', 'technews-theme'); ?></a>

    <header id="masthead" class="site-header <?php echo get_theme_mod('sticky_header', true) ? 'sticky' : ''; ?>" role="banner">
        <div class="header-container">
            
            <!-- Site Branding -->
            <div class="site-branding">
                <?php
                // Custom logo
                if (has_custom_logo()) :
                    the_custom_logo();
                endif;
                ?>
                
                <div class="site-identity">
                    <?php if (is_front_page() && is_home()) : ?>
                        <h1 class="site-title">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                <?php bloginfo('name'); ?>
                            </a>
                        </h1>
                    <?php else : ?>
                        <p class="site-title">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                <?php bloginfo('name'); ?>
                            </a>
                        </p>
                    <?php endif; ?>

                    <?php
                    $technews_description = get_bloginfo('description', 'display');
                    if ($technews_description || is_customize_preview()) :
                    ?>
                        <p class="site-description"><?php echo $technews_description; ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Primary Navigation -->
            <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="<?php esc_attr_e('Primary Menu', 'technews-theme'); ?>">
                
                <!-- Mobile Menu Toggle Button -->
                <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false" aria-label="<?php esc_attr_e('Toggle navigation', 'technews-theme'); ?>">
                    <span class="screen-reader-text"><?php esc_html_e('Menu', 'technews-theme'); ?></span>
                    <span></span>
                    <span></span>
                    <span></span>
                </button>

                <?php
                // Primary navigation menu
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'menu_class'     => 'nav-menu',
                    'container'      => false,
                    'fallback_cb'    => 'technews_default_menu',
                ));
                ?>

            </nav>

            <!-- Search Form (Desktop) -->
            <div class="header-search desktop-only">
                <?php get_search_form(); ?>
            </div>

        </div>

        <!-- Mobile Search (Hidden by default) -->
        <div class="mobile-search-container">
            <div class="container">
                <?php get_search_form(); ?>
            </div>
        </div>

    </header>

    <?php
    // Breaking News Ticker (if enabled in customizer)
    if (get_theme_mod('enable_breaking_news', false)) :
        $breaking_news_posts = get_posts(array(
            'numberposts' => 5,
            'meta_key'    => '_technews_breaking_news',
            'meta_value'  => '1',
            'post_status' => 'publish'
        ));

        if (!empty($breaking_news_posts)) :
    ?>
        <div class="breaking-news-ticker">
            <div class="container">
                <div class="breaking-news-content">
                    <span class="breaking-news-label">
                        <?php esc_html_e('Breaking News', 'technews-theme'); ?>
                    </span>
                    <div class="breaking-news-items">
                        <?php foreach ($breaking_news_posts as $post) : ?>
                            <div class="breaking-news-item">
                                <a href="<?php echo get_permalink($post->ID); ?>">
                                    <?php echo get_the_title($post->ID); ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php 
        endif;
    endif; 
    ?>

    <!-- Breadcrumbs -->
    <?php if (!is_front_page() && !is_home()) : ?>
        <div class="breadcrumbs-container">
            <div class="container">
                <nav class="breadcrumbs" aria-label="<?php esc_attr_e('Breadcrumb Navigation', 'technews-theme'); ?>">
                    <?php technews_breadcrumbs(); ?>
                </nav>
            </div>
        </div>
    <?php endif; ?>

</div>

<?php
/**
 * Default menu fallback
 */
function technews_default_menu() {
    echo '<ul id="primary-menu" class="nav-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . esc_html__('Home', 'technews-theme') . '</a></li>';
    
    // Get some categories to show
    $categories = get_categories(array('number' => 5));
    foreach ($categories as $category) {
        echo '<li><a href="' . esc_url(get_category_link($category->term_id)) . '">' . esc_html($category->name) . '</a></li>';
    }
    
    echo '</ul>';
}
?>
