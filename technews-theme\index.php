<?php

/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * E.g., it puts together the home page when no home.php file exists.
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

get_header(); ?>

<div class="site-content">
    <div class="container">
        <div class="content-area <?php echo is_active_sidebar('sidebar-1') ? 'has-sidebar' : ''; ?>">
            <main id="main" class="site-main" role="main">

                <?php if (is_home() && !is_front_page()) : ?>
                    <header class="page-header">
                        <h1 class="page-title"><?php single_post_title(); ?></h1>
                    </header>
                <?php endif; ?>

                <?php if (have_posts()) : ?>

                    <?php if (is_home() || is_front_page()) : ?>
                        <!-- News Grid Layout for Home Page -->
                        <div class="news-grid">
                            <?php
                            $post_count = 0;
                            while (have_posts()) :
                                the_post();
                                $post_count++;

                                // Featured post (first post)
                                if ($post_count === 1) :
                                    if (locate_template('template-parts/content-featured.php')) {
                                        get_template_part('template-parts/content', 'featured');
                                    } else {
                                        // Fallback content
                            ?>
                                        <article class="featured-post">
                                            <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                                            <?php the_excerpt(); ?>
                                        </article>
                                    <?php
                                    }
                                else :
                                    if (locate_template('template-parts/content-grid.php')) {
                                        get_template_part('template-parts/content', 'grid');
                                    } else {
                                        // Fallback content
                                    ?>
                                        <article class="grid-post">
                                            <h3><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                                            <?php the_excerpt(); ?>
                                        </article>
                            <?php
                                    }
                                endif;

                            endwhile;
                            ?>
                        </div>

                    <?php else : ?>
                        <!-- Standard Blog Layout -->
                        <div class="posts-container">
                            <?php
                            while (have_posts()) :
                                the_post();
                                if (locate_template('template-parts/content.php')) {
                                    get_template_part('template-parts/content', get_post_type());
                                } else {
                                    // Fallback content
                            ?>
                                    <article class="blog-post">
                                        <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                                        <?php the_content(); ?>
                                    </article>
                            <?php
                                }
                            endwhile;
                            ?>
                        </div>

                    <?php endif; ?>

                    <?php
                    // Pagination
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => __('&laquo; Previous', 'technews-theme'),
                        'next_text' => __('Next &raquo;', 'technews-theme'),
                        'class'     => 'pagination-wrapper'
                    ));
                    ?>

                <?php else : ?>

                    <section class="no-results not-found">
                        <header class="page-header">
                            <h1 class="page-title"><?php esc_html_e('Nothing here', 'technews-theme'); ?></h1>
                        </header>

                        <div class="page-content">
                            <?php if (is_home() && current_user_can('publish_posts')) : ?>
                                <p>
                                    <?php
                                    printf(
                                        wp_kses(
                                            __('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'technews-theme'),
                                            array(
                                                'a' => array(
                                                    'href' => array(),
                                                ),
                                            )
                                        ),
                                        esc_url(admin_url('post-new.php'))
                                    );
                                    ?>
                                </p>
                            <?php elseif (is_search()) : ?>
                                <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'technews-theme'); ?></p>
                                <?php get_search_form(); ?>
                            <?php else : ?>
                                <p><?php esc_html_e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'technews-theme'); ?></p>
                                <?php get_search_form(); ?>
                            <?php endif; ?>
                        </div>
                    </section>

                <?php endif; ?>

            </main>

            <?php if (is_active_sidebar('sidebar-1')) : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>

        </div>
    </div>
</div>

<?php get_footer(); ?>