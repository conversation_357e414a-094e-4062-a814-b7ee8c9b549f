<?php
/**
 * The template for displaying search results pages
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

get_header();
?>

<div class="site-content">
    <div class="container">
        <div class="content-area <?php echo is_active_sidebar('sidebar-1') ? 'has-sidebar' : ''; ?>">
            <main id="main" class="site-main" role="main">

                <?php if (have_posts()) : ?>

                    <header class="page-header">
                        <h1 class="page-title">
                            <?php
                            printf(
                                esc_html__('Search Results for: %s', 'technews-theme'),
                                '<span class="search-term">' . get_search_query() . '</span>'
                            );
                            ?>
                        </h1>
                        
                        <!-- Search Results Meta -->
                        <div class="search-meta">
                            <?php
                            global $wp_query;
                            $total_results = $wp_query->found_posts;
                            $posts_per_page = get_option('posts_per_page');
                            $current_page = max(1, get_query_var('paged'));
                            $total_pages = ceil($total_results / $posts_per_page);
                            ?>
                            
                            <span class="search-count">
                                <?php
                                printf(
                                    _n('%s result found', '%s results found', $total_results, 'technews-theme'),
                                    number_format_i18n($total_results)
                                );
                                ?>
                            </span>
                            
                            <?php if ($total_pages > 1) : ?>
                                <span class="search-pagination-info">
                                    <?php
                                    printf(
                                        esc_html__('Page %1$s of %2$s', 'technews-theme'),
                                        number_format_i18n($current_page),
                                        number_format_i18n($total_pages)
                                    );
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <!-- Search Form -->
                        <div class="search-form-container">
                            <?php get_search_form(); ?>
                        </div>
                    </header>

                    <!-- Search Results -->
                    <div class="search-results-container">
                        <?php
                        while (have_posts()) :
                            the_post();
                        ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('search-result'); ?>>
                                
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="search-result-thumbnail">
                                        <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                                            <?php the_post_thumbnail('technews-small', array(
                                                'alt' => the_title_attribute(array('echo' => false)),
                                            )); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <div class="search-result-content">
                                    
                                    <header class="entry-header">
                                        <?php
                                        // Post type indicator
                                        $post_type = get_post_type();
                                        if ($post_type !== 'post') :
                                        ?>
                                            <span class="post-type-badge">
                                                <?php echo esc_html(get_post_type_object($post_type)->labels->singular_name); ?>
                                            </span>
                                        <?php endif; ?>

                                        <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>

                                        <div class="entry-meta">
                                            <?php if ('post' === get_post_type()) : ?>
                                                <?php technews_posted_on(); ?>
                                                <?php technews_posted_by(); ?>
                                            <?php endif; ?>
                                            
                                            <?php
                                            // Post categories
                                            if ('post' === get_post_type()) :
                                                $categories = get_the_category();
                                                if (!empty($categories)) :
                                            ?>
                                                <span class="entry-categories">
                                                    <?php foreach ($categories as $category) : ?>
                                                        <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="category-link">
                                                            <?php echo esc_html($category->name); ?>
                                                        </a>
                                                    <?php endforeach; ?>
                                                </span>
                                            <?php 
                                                endif;
                                            endif; 
                                            ?>
                                        </div>
                                    </header>

                                    <div class="entry-summary">
                                        <?php
                                        // Highlight search terms in excerpt
                                        $excerpt = get_the_excerpt();
                                        $search_query = get_search_query();
                                        
                                        if (!empty($search_query)) {
                                            $excerpt = preg_replace(
                                                '/(' . preg_quote($search_query, '/') . ')/i',
                                                '<mark>$1</mark>',
                                                $excerpt
                                            );
                                        }
                                        
                                        echo wp_kses_post($excerpt);
                                        ?>
                                    </div>

                                    <footer class="entry-footer">
                                        <a href="<?php the_permalink(); ?>" class="read-more-link">
                                            <?php esc_html_e('Read More', 'technews-theme'); ?>
                                            <span class="screen-reader-text"><?php the_title(); ?></span>
                                        </a>
                                    </footer>

                                </div>

                            </article>
                        <?php endwhile; ?>
                    </div>

                    <?php
                    // Pagination
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => __('&laquo; Previous', 'technews-theme'),
                        'next_text' => __('Next &raquo;', 'technews-theme'),
                        'class'     => 'pagination-wrapper'
                    ));
                    ?>

                <?php else : ?>

                    <section class="no-results not-found">
                        <header class="page-header">
                            <h1 class="page-title">
                                <?php
                                printf(
                                    esc_html__('No results for: %s', 'technews-theme'),
                                    '<span class="search-term">' . get_search_query() . '</span>'
                                );
                                ?>
                            </h1>
                        </header>

                        <div class="page-content">
                            <p><?php esc_html_e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'technews-theme'); ?></p>
                            
                            <!-- Search Form -->
                            <div class="search-form-container">
                                <?php get_search_form(); ?>
                            </div>

                            <!-- Search Suggestions -->
                            <div class="search-suggestions">
                                <h3><?php esc_html_e('Search Suggestions:', 'technews-theme'); ?></h3>
                                <ul>
                                    <li><?php esc_html_e('Make sure all words are spelled correctly.', 'technews-theme'); ?></li>
                                    <li><?php esc_html_e('Try different keywords.', 'technews-theme'); ?></li>
                                    <li><?php esc_html_e('Try more general keywords.', 'technews-theme'); ?></li>
                                    <li><?php esc_html_e('Try fewer keywords.', 'technews-theme'); ?></li>
                                </ul>
                            </div>

                            <!-- Popular Categories -->
                            <?php
                            $popular_categories = get_categories(array(
                                'orderby' => 'count',
                                'order'   => 'DESC',
                                'number'  => 6
                            ));
                            
                            if (!empty($popular_categories)) :
                            ?>
                                <div class="popular-categories">
                                    <h3><?php esc_html_e('Browse Popular Categories:', 'technews-theme'); ?></h3>
                                    <ul class="category-list">
                                        <?php foreach ($popular_categories as $category) : ?>
                                            <li>
                                                <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>">
                                                    <?php echo esc_html($category->name); ?>
                                                    <span class="category-count">(<?php echo $category->count; ?>)</span>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        </div>
                    </section>

                <?php endif; ?>

            </main>

            <?php if (is_active_sidebar('sidebar-1')) : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<?php get_footer(); ?>
