<?php
/**
 * Template part for displaying featured posts on the home page
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */
?>

<article id="post-<?php the_ID(); ?>" <?php post_class('featured-post'); ?>>
    
    <?php if (has_post_thumbnail()) : ?>
        <div class="featured-post-thumbnail">
            <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                <?php the_post_thumbnail('technews-featured', array(
                    'alt' => the_title_attribute(array('echo' => false)),
                )); ?>
            </a>
            
            <!-- Post Category Badge -->
            <?php
            $categories = get_the_category();
            if (!empty($categories)) :
                $primary_category = $categories[0];
            ?>
                <span class="post-category-badge">
                    <a href="<?php echo esc_url(get_category_link($primary_category->term_id)); ?>">
                        <?php echo esc_html($primary_category->name); ?>
                    </a>
                </span>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="featured-post-content">
        
        <header class="entry-header">
            <?php
            // Post categories
            $categories = get_the_category();
            if (!empty($categories)) :
            ?>
                <div class="entry-categories">
                    <?php foreach ($categories as $category) : ?>
                        <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>" class="category-link">
                            <?php echo esc_html($category->name); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>

            <div class="entry-meta">
                <?php technews_posted_on(); ?>
                <?php technews_posted_by(); ?>
                
                <?php if (comments_open() || get_comments_number()) : ?>
                    <span class="comments-link">
                        <?php comments_popup_link(
                            esc_html__('Leave a comment', 'technews-theme'),
                            esc_html__('1 Comment', 'technews-theme'),
                            esc_html__('% Comments', 'technews-theme')
                        ); ?>
                    </span>
                <?php endif; ?>

                <!-- Reading time estimate -->
                <span class="reading-time">
                    <?php echo technews_reading_time(); ?>
                </span>
            </div>
        </header>

        <div class="entry-summary">
            <?php
            // Show excerpt or first 150 words
            if (has_excerpt()) {
                the_excerpt();
            } else {
                echo wp_trim_words(get_the_content(), 30, '...');
            }
            ?>
        </div>

        <footer class="entry-footer">
            <a href="<?php the_permalink(); ?>" class="read-more-link btn btn-primary">
                <?php esc_html_e('Read More', 'technews-theme'); ?>
                <span class="screen-reader-text"><?php the_title(); ?></span>
            </a>

            <?php if (get_edit_post_link()) : ?>
                <span class="edit-link">
                    <?php edit_post_link(
                        sprintf(
                            wp_kses(
                                __('Edit <span class="screen-reader-text">"%s"</span>', 'technews-theme'),
                                array('span' => array('class' => array()))
                            ),
                            get_the_title()
                        ),
                        '<span class="edit-link">',
                        '</span>'
                    ); ?>
                </span>
            <?php endif; ?>
        </footer>

    </div>

</article><!-- #post-<?php the_ID(); ?> -->
