<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

get_header();
?>

<div class="site-content">
    <div class="container">
        <div class="content-area">
            <main id="main" class="site-main" role="main">

                <section class="error-404 not-found">
                    
                    <div class="error-404-content">
                        
                        <!-- 404 Illustration -->
                        <div class="error-404-illustration">
                            <div class="error-number">404</div>
                            <div class="error-icon">
                                <svg width="120" height="120" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="m9 9 6 6"></path>
                                    <path d="m15 9-6 6"></path>
                                </svg>
                            </div>
                        </div>

                        <header class="page-header">
                            <h1 class="page-title"><?php esc_html_e('Oops! That page can&rsquo;t be found.', 'technews-theme'); ?></h1>
                            <p class="error-description">
                                <?php esc_html_e('It looks like nothing was found at this location. Maybe try one of the links below or a search?', 'technews-theme'); ?>
                            </p>
                        </header>

                        <div class="page-content">
                            
                            <!-- Search Form -->
                            <div class="error-search">
                                <h3><?php esc_html_e('Search for:', 'technews-theme'); ?></h3>
                                <?php get_search_form(); ?>
                            </div>

                            <div class="error-404-widgets">
                                
                                <!-- Recent Posts -->
                                <div class="error-widget">
                                    <h3><?php esc_html_e('Recent Articles', 'technews-theme'); ?></h3>
                                    <?php
                                    $recent_posts = wp_get_recent_posts(array(
                                        'numberposts' => 5,
                                        'post_status' => 'publish'
                                    ));
                                    
                                    if (!empty($recent_posts)) :
                                    ?>
                                        <ul class="recent-posts-list">
                                            <?php foreach ($recent_posts as $post) : ?>
                                                <li>
                                                    <a href="<?php echo get_permalink($post['ID']); ?>">
                                                        <?php echo esc_html($post['post_title']); ?>
                                                    </a>
                                                    <span class="post-date">
                                                        <?php echo get_the_date('', $post['ID']); ?>
                                                    </span>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </div>

                                <!-- Popular Categories -->
                                <div class="error-widget">
                                    <h3><?php esc_html_e('Popular Categories', 'technews-theme'); ?></h3>
                                    <?php
                                    $categories = get_categories(array(
                                        'orderby' => 'count',
                                        'order'   => 'DESC',
                                        'number'  => 8
                                    ));
                                    
                                    if (!empty($categories)) :
                                    ?>
                                        <ul class="categories-list">
                                            <?php foreach ($categories as $category) : ?>
                                                <li>
                                                    <a href="<?php echo get_category_link($category->term_id); ?>">
                                                        <?php echo esc_html($category->name); ?>
                                                        <span class="category-count">(<?php echo $category->count; ?>)</span>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </div>

                                <!-- Tag Cloud -->
                                <div class="error-widget">
                                    <h3><?php esc_html_e('Popular Tags', 'technews-theme'); ?></h3>
                                    <?php
                                    $tags = get_tags(array(
                                        'orderby' => 'count',
                                        'order'   => 'DESC',
                                        'number'  => 15
                                    ));
                                    
                                    if (!empty($tags)) :
                                    ?>
                                        <div class="tags-cloud">
                                            <?php foreach ($tags as $tag) : ?>
                                                <a href="<?php echo get_tag_link($tag->term_id); ?>" class="tag-link">
                                                    <?php echo esc_html($tag->name); ?>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Site Navigation -->
                                <div class="error-widget">
                                    <h3><?php esc_html_e('Site Navigation', 'technews-theme'); ?></h3>
                                    <ul class="site-navigation-list">
                                        <li><a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Home', 'technews-theme'); ?></a></li>
                                        <?php
                                        // Get pages
                                        $pages = get_pages(array(
                                            'sort_order' => 'ASC',
                                            'sort_column' => 'menu_order',
                                            'number' => 5
                                        ));
                                        
                                        foreach ($pages as $page) :
                                        ?>
                                            <li>
                                                <a href="<?php echo get_permalink($page->ID); ?>">
                                                    <?php echo esc_html($page->post_title); ?>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>

                            </div>

                            <!-- Help Text -->
                            <div class="error-help">
                                <h3><?php esc_html_e('Need Help?', 'technews-theme'); ?></h3>
                                <p><?php esc_html_e('If you think this is an error, please contact us and let us know what you were looking for.', 'technews-theme'); ?></p>
                                
                                <!-- Contact Button (if contact page exists) -->
                                <?php
                                $contact_page = get_page_by_path('contact');
                                if ($contact_page) :
                                ?>
                                    <a href="<?php echo get_permalink($contact_page->ID); ?>" class="btn btn-primary">
                                        <?php esc_html_e('Contact Us', 'technews-theme'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <!-- Back to Home Button -->
                                <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-secondary">
                                    <?php esc_html_e('Back to Home', 'technews-theme'); ?>
                                </a>
                            </div>

                        </div>

                    </div>

                </section>

            </main>
        </div>
    </div>
</div>

<?php get_footer(); ?>
