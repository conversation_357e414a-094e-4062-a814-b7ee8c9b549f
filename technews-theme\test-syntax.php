<?php
/**
 * Simple syntax test file
 * Run this to check for PHP syntax errors
 */

// Test if functions.php has syntax errors
$functions_file = __DIR__ . '/functions.php';
if (file_exists($functions_file)) {
    $content = file_get_contents($functions_file);
    if ($content !== false) {
        echo "✓ functions.php file exists and is readable\n";
        
        // Check for basic syntax issues
        if (substr_count($content, '<?php') > 0) {
            echo "✓ functions.php has PHP opening tags\n";
        }
        
        // Check for unclosed brackets/braces
        $open_braces = substr_count($content, '{');
        $close_braces = substr_count($content, '}');
        if ($open_braces === $close_braces) {
            echo "✓ functions.php has balanced braces\n";
        } else {
            echo "✗ functions.php has unbalanced braces: $open_braces open, $close_braces close\n";
        }
        
        $open_parens = substr_count($content, '(');
        $close_parens = substr_count($content, ')');
        if ($open_parens === $close_parens) {
            echo "✓ functions.php has balanced parentheses\n";
        } else {
            echo "✗ functions.php has unbalanced parentheses: $open_parens open, $close_parens close\n";
        }
    }
} else {
    echo "✗ functions.php file not found\n";
}

// Test other critical files
$critical_files = ['style.css', 'index.php', 'header.php', 'footer.php'];
foreach ($critical_files as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "✓ $file exists\n";
    } else {
        echo "✗ $file missing\n";
    }
}

echo "\nSyntax test complete. If you see any ✗ marks above, those need to be fixed.\n";
echo "Note: WordPress function 'undefined' errors in IDE are normal - they exist in WordPress.\n";
?>
