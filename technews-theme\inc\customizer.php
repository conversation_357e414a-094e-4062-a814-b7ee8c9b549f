<?php
/**
 * TechNews Theme Customizer
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 */
function technews_customize_register($wp_customize) {
    $wp_customize->get_setting('blogname')->transport         = 'postMessage';
    $wp_customize->get_setting('blogdescription')->transport  = 'postMessage';
    $wp_customize->get_setting('header_textcolor')->transport = 'postMessage';

    if (isset($wp_customize->selective_refresh)) {
        $wp_customize->selective_refresh->add_partial('blogname', array(
            'selector'        => '.site-title a',
            'render_callback' => 'technews_customize_partial_blogname',
        ));
        $wp_customize->selective_refresh->add_partial('blogdescription', array(
            'selector'        => '.site-description',
            'render_callback' => 'technews_customize_partial_blogdescription',
        ));
    }

    // Theme Options Panel
    $wp_customize->add_panel('technews_theme_options', array(
        'title'       => esc_html__('Theme Options', 'technews-theme'),
        'description' => esc_html__('Customize your theme settings', 'technews-theme'),
        'priority'    => 30,
    ));

    // Header Section
    $wp_customize->add_section('technews_header_options', array(
        'title'    => esc_html__('Header Options', 'technews-theme'),
        'panel'    => 'technews_theme_options',
        'priority' => 10,
    ));

    // Sticky Header
    $wp_customize->add_setting('sticky_header', array(
        'default'           => true,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('sticky_header', array(
        'label'    => esc_html__('Enable Sticky Header', 'technews-theme'),
        'section'  => 'technews_header_options',
        'type'     => 'checkbox',
    ));

    // Breaking News
    $wp_customize->add_setting('enable_breaking_news', array(
        'default'           => false,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_breaking_news', array(
        'label'    => esc_html__('Enable Breaking News Ticker', 'technews-theme'),
        'section'  => 'technews_header_options',
        'type'     => 'checkbox',
    ));

    // Layout Section
    $wp_customize->add_section('technews_layout_options', array(
        'title'    => esc_html__('Layout Options', 'technews-theme'),
        'panel'    => 'technews_theme_options',
        'priority' => 20,
    ));

    // Sidebar Position
    $wp_customize->add_setting('sidebar_position', array(
        'default'           => 'right',
        'sanitize_callback' => 'technews_sanitize_select',
    ));

    $wp_customize->add_control('sidebar_position', array(
        'label'    => esc_html__('Sidebar Position', 'technews-theme'),
        'section'  => 'technews_layout_options',
        'type'     => 'select',
        'choices'  => array(
            'left'  => esc_html__('Left', 'technews-theme'),
            'right' => esc_html__('Right', 'technews-theme'),
        ),
    ));

    // Blog Section
    $wp_customize->add_section('technews_blog_options', array(
        'title'    => esc_html__('Blog Options', 'technews-theme'),
        'panel'    => 'technews_theme_options',
        'priority' => 30,
    ));

    // Show Author Bio
    $wp_customize->add_setting('show_author_bio', array(
        'default'           => true,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_author_bio', array(
        'label'    => esc_html__('Show Author Bio on Single Posts', 'technews-theme'),
        'section'  => 'technews_blog_options',
        'type'     => 'checkbox',
    ));

    // Show Related Posts
    $wp_customize->add_setting('show_related_posts', array(
        'default'           => true,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_related_posts', array(
        'label'    => esc_html__('Show Related Posts on Single Posts', 'technews-theme'),
        'section'  => 'technews_blog_options',
        'type'     => 'checkbox',
    ));

    // Show Social Sharing
    $wp_customize->add_setting('show_social_sharing', array(
        'default'           => true,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_social_sharing', array(
        'label'    => esc_html__('Show Social Sharing Buttons', 'technews-theme'),
        'section'  => 'technews_blog_options',
        'type'     => 'checkbox',
    ));

    // Footer Section
    $wp_customize->add_section('technews_footer_options', array(
        'title'    => esc_html__('Footer Options', 'technews-theme'),
        'panel'    => 'technews_theme_options',
        'priority' => 40,
    ));

    // Copyright Text
    $wp_customize->add_setting('copyright_text', array(
        'default'           => '',
        'sanitize_callback' => 'wp_kses_post',
    ));

    $wp_customize->add_control('copyright_text', array(
        'label'    => esc_html__('Copyright Text', 'technews-theme'),
        'section'  => 'technews_footer_options',
        'type'     => 'textarea',
    ));

    // Show Theme Credit
    $wp_customize->add_setting('show_theme_credit', array(
        'default'           => true,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_theme_credit', array(
        'label'    => esc_html__('Show Theme Credit', 'technews-theme'),
        'section'  => 'technews_footer_options',
        'type'     => 'checkbox',
    ));

    // Show Back to Top Button
    $wp_customize->add_setting('show_back_to_top', array(
        'default'           => true,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_back_to_top', array(
        'label'    => esc_html__('Show Back to Top Button', 'technews-theme'),
        'section'  => 'technews_footer_options',
        'type'     => 'checkbox',
    ));

    // Newsletter Section
    $wp_customize->add_section('technews_newsletter_options', array(
        'title'    => esc_html__('Newsletter Options', 'technews-theme'),
        'panel'    => 'technews_theme_options',
        'priority' => 50,
    ));

    // Show Newsletter Widget
    $wp_customize->add_setting('show_newsletter_widget', array(
        'default'           => false,
        'sanitize_callback' => 'technews_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_newsletter_widget', array(
        'label'    => esc_html__('Show Newsletter Widget in Sidebar', 'technews-theme'),
        'section'  => 'technews_newsletter_options',
        'type'     => 'checkbox',
    ));

    // Newsletter Description
    $wp_customize->add_setting('newsletter_description', array(
        'default'           => esc_html__('Subscribe to our newsletter to get the latest tech news delivered to your inbox.', 'technews-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('newsletter_description', array(
        'label'    => esc_html__('Newsletter Description', 'technews-theme'),
        'section'  => 'technews_newsletter_options',
        'type'     => 'textarea',
    ));

    // Colors Section
    $wp_customize->add_section('technews_color_options', array(
        'title'    => esc_html__('Color Options', 'technews-theme'),
        'panel'    => 'technews_theme_options',
        'priority' => 60,
    ));

    // Primary Color
    $wp_customize->add_setting('primary_color', array(
        'default'           => '#007cba',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_color', array(
        'label'    => esc_html__('Primary Color', 'technews-theme'),
        'section'  => 'technews_color_options',
    )));

    // Secondary Color
    $wp_customize->add_setting('secondary_color', array(
        'default'           => '#005a87',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'secondary_color', array(
        'label'    => esc_html__('Secondary Color', 'technews-theme'),
        'section'  => 'technews_color_options',
    )));
}
add_action('customize_register', 'technews_customize_register');

/**
 * Render the site title for the selective refresh partial.
 */
function technews_customize_partial_blogname() {
    bloginfo('name');
}

/**
 * Render the site tagline for the selective refresh partial.
 */
function technews_customize_partial_blogdescription() {
    bloginfo('description');
}

/**
 * Sanitize checkbox
 */
function technews_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Sanitize select
 */
function technews_sanitize_select($input, $setting) {
    $input = sanitize_key($input);
    $choices = $setting->manager->get_control($setting->id)->choices;
    return (array_key_exists($input, $choices) ? $input : $setting->default);
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function technews_customize_preview_js() {
    wp_enqueue_script('technews-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array('customize-preview'), TECHNEWS_VERSION, true);
}
add_action('customize_preview_init', 'technews_customize_preview_js');

/**
 * Output custom CSS for customizer options
 */
function technews_customizer_css() {
    $primary_color = get_theme_mod('primary_color', '#007cba');
    $secondary_color = get_theme_mod('secondary_color', '#005a87');

    if ($primary_color !== '#007cba' || $secondary_color !== '#005a87') {
        ?>
        <style type="text/css">
            :root {
                --primary-color: <?php echo esc_attr($primary_color); ?>;
                --secondary-color: <?php echo esc_attr($secondary_color); ?>;
            }
            
            a, .category-link:hover, .tag-cloud-link:hover,
            .btn, .post-category-badge, .widget-title,
            .footer-widget-area .widget-title, .social-share-btn.facebook,
            .back-to-top, .author-posts-link {
                background-color: <?php echo esc_attr($primary_color); ?>;
            }
            
            a:hover, .btn:hover, .back-to-top:hover,
            .social-navigation a:hover, .author-social a:hover {
                background-color: <?php echo esc_attr($secondary_color); ?>;
            }
            
            .entry-meta a:hover, .grid-post .entry-title a:hover,
            .widget a:hover, .footer-widget-area a:hover,
            .footer-navigation a:hover, .author-name a:hover {
                color: <?php echo esc_attr($primary_color); ?>;
            }
        </style>
        <?php
    }
}
add_action('wp_head', 'technews_customizer_css');
