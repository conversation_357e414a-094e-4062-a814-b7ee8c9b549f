<?php
/**
 * Minimal TechNews Theme functions - Use this if main functions.php causes errors
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme version
 */
define('TECHNEWS_VERSION', '1.0.0');

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function technews_setup() {
    // Make theme available for translation
    load_theme_textdomain('technews-theme', get_template_directory() . '/languages');

    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'technews-theme'),
    ));

    // Switch default core markup to output valid HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // Add support for custom logo
    add_theme_support('custom-logo');
}
add_action('after_setup_theme', 'technews_setup');

/**
 * Register widget areas
 */
function technews_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'technews-theme'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here.', 'technews-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'technews_widgets_init');

/**
 * Enqueue scripts and styles
 */
function technews_scripts() {
    wp_enqueue_style('technews-style', get_stylesheet_uri(), array(), TECHNEWS_VERSION);
    wp_style_add_data('technews-style', 'rtl', 'replace');
}
add_action('wp_enqueue_scripts', 'technews_scripts');

/**
 * Essential template functions
 */
if (!function_exists('technews_posted_on')) {
    function technews_posted_on() {
        echo '<span class="posted-on">' . get_the_date() . '</span>';
    }
}

if (!function_exists('technews_posted_by')) {
    function technews_posted_by() {
        echo '<span class="byline">by ' . get_the_author() . '</span>';
    }
}

if (!function_exists('technews_entry_footer')) {
    function technews_entry_footer() {
        $categories_list = get_the_category_list(', ');
        if ($categories_list) {
            echo '<span class="cat-links">Posted in ' . $categories_list . '</span>';
        }
    }
}

if (!function_exists('technews_reading_time')) {
    function technews_reading_time() {
        $content = get_post_field('post_content', get_the_ID());
        $word_count = str_word_count(strip_tags($content));
        $reading_time = ceil($word_count / 200);
        return $reading_time . ' min read';
    }
}

if (!function_exists('technews_social_sharing_buttons')) {
    function technews_social_sharing_buttons() {
        $post_url = urlencode(get_permalink());
        $post_title = urlencode(get_the_title());
        
        echo '<div class="social-sharing-buttons">';
        echo '<a href="https://www.facebook.com/sharer/sharer.php?u=' . $post_url . '" target="_blank">Facebook</a> ';
        echo '<a href="https://twitter.com/intent/tweet?url=' . $post_url . '&text=' . $post_title . '" target="_blank">Twitter</a>';
        echo '</div>';
    }
}

if (!function_exists('technews_breadcrumbs')) {
    function technews_breadcrumbs() {
        if (is_front_page()) return;
        
        echo '<nav class="breadcrumbs">';
        echo '<a href="' . home_url('/') . '">Home</a>';
        
        if (is_single()) {
            echo ' / <span>' . get_the_title() . '</span>';
        } elseif (is_page()) {
            echo ' / <span>' . get_the_title() . '</span>';
        }
        
        echo '</nav>';
    }
}

if (!function_exists('technews_default_menu')) {
    function technews_default_menu() {
        echo '<ul id="primary-menu" class="nav-menu">';
        echo '<li><a href="' . home_url('/') . '">Home</a></li>';
        echo '</ul>';
    }
}
