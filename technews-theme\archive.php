<?php
/**
 * The template for displaying archive pages
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

get_header();
?>

<div class="site-content">
    <div class="container">
        <div class="content-area <?php echo is_active_sidebar('sidebar-1') ? 'has-sidebar' : ''; ?>">
            <main id="main" class="site-main" role="main">

                <?php if (have_posts()) : ?>

                    <header class="page-header">
                        <?php
                        the_archive_title('<h1 class="page-title">', '</h1>');
                        the_archive_description('<div class="archive-description">', '</div>');
                        ?>
                        
                        <!-- Archive Meta Information -->
                        <div class="archive-meta">
                            <?php
                            global $wp_query;
                            $total_posts = $wp_query->found_posts;
                            $posts_per_page = get_option('posts_per_page');
                            $current_page = max(1, get_query_var('paged'));
                            $total_pages = ceil($total_posts / $posts_per_page);
                            ?>
                            
                            <span class="archive-count">
                                <?php
                                printf(
                                    _n('%s article found', '%s articles found', $total_posts, 'technews-theme'),
                                    number_format_i18n($total_posts)
                                );
                                ?>
                            </span>
                            
                            <?php if ($total_pages > 1) : ?>
                                <span class="archive-pagination-info">
                                    <?php
                                    printf(
                                        esc_html__('Page %1$s of %2$s', 'technews-theme'),
                                        number_format_i18n($current_page),
                                        number_format_i18n($total_pages)
                                    );
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </header>

                    <!-- Archive Layout Options -->
                    <div class="archive-controls">
                        <div class="view-toggle">
                            <button class="view-toggle-btn active" data-view="grid" aria-label="<?php esc_attr_e('Grid view', 'technews-theme'); ?>">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z"/>
                                </svg>
                            </button>
                            <button class="view-toggle-btn" data-view="list" aria-label="<?php esc_attr_e('List view', 'technews-theme'); ?>">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- Sort Options -->
                        <div class="sort-options">
                            <label for="archive-sort" class="screen-reader-text">
                                <?php esc_html_e('Sort posts by', 'technews-theme'); ?>
                            </label>
                            <select id="archive-sort" class="archive-sort-select">
                                <option value="date" <?php selected(get_query_var('orderby'), 'date'); ?>>
                                    <?php esc_html_e('Latest', 'technews-theme'); ?>
                                </option>
                                <option value="title" <?php selected(get_query_var('orderby'), 'title'); ?>>
                                    <?php esc_html_e('Title', 'technews-theme'); ?>
                                </option>
                                <option value="comment_count" <?php selected(get_query_var('orderby'), 'comment_count'); ?>>
                                    <?php esc_html_e('Most Commented', 'technews-theme'); ?>
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- Posts Container -->
                    <div class="archive-posts-container" data-view="grid">
                        <div class="posts-grid">
                            <?php
                            while (have_posts()) :
                                the_post();
                                get_template_part('template-parts/content', 'grid');
                            endwhile;
                            ?>
                        </div>
                    </div>

                    <?php
                    // Pagination
                    the_posts_pagination(array(
                        'mid_size'  => 2,
                        'prev_text' => __('&laquo; Previous', 'technews-theme'),
                        'next_text' => __('Next &raquo;', 'technews-theme'),
                        'class'     => 'pagination-wrapper'
                    ));
                    ?>

                <?php else : ?>

                    <section class="no-results not-found">
                        <header class="page-header">
                            <h1 class="page-title"><?php esc_html_e('Nothing here', 'technews-theme'); ?></h1>
                        </header>

                        <div class="page-content">
                            <p><?php esc_html_e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'technews-theme'); ?></p>
                            <?php get_search_form(); ?>
                        </div>
                    </section>

                <?php endif; ?>

            </main>

            <?php if (is_active_sidebar('sidebar-1')) : ?>
                <?php get_sidebar(); ?>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<?php get_footer(); ?>
