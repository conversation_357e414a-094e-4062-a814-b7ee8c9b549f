<?php
/**
 * The sidebar containing the main widget area
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

if (!is_active_sidebar('sidebar-1')) {
    return;
}
?>

<aside id="secondary" class="widget-area sidebar" role="complementary" aria-label="<?php esc_attr_e('Sidebar', 'technews-theme'); ?>">
    
    <?php
    // Add a search widget if no search widget is already in the sidebar
    $sidebar_widgets = wp_get_sidebars_widgets();
    $has_search_widget = false;
    
    if (isset($sidebar_widgets['sidebar-1'])) {
        foreach ($sidebar_widgets['sidebar-1'] as $widget_id) {
            if (strpos($widget_id, 'search') !== false) {
                $has_search_widget = true;
                break;
            }
        }
    }
    
    // Show search widget if none exists in sidebar
    if (!$has_search_widget) :
    ?>
        <section class="widget widget_search">
            <h3 class="widget-title"><?php esc_html_e('Search', 'technews-theme'); ?></h3>
            <?php get_search_form(); ?>
        </section>
    <?php endif; ?>

    <?php dynamic_sidebar('sidebar-1'); ?>

    <?php
    // Add default widgets if sidebar is empty
    $sidebar_widgets = wp_get_sidebars_widgets();
    if (empty($sidebar_widgets['sidebar-1'])) :
    ?>
        
        <!-- Recent Posts Widget -->
        <section class="widget widget_recent_entries">
            <h3 class="widget-title"><?php esc_html_e('Recent Posts', 'technews-theme'); ?></h3>
            <ul>
                <?php
                $recent_posts = wp_get_recent_posts(array(
                    'numberposts' => 5,
                    'post_status' => 'publish'
                ));
                
                foreach ($recent_posts as $post) :
                ?>
                    <li>
                        <a href="<?php echo get_permalink($post['ID']); ?>">
                            <?php echo esc_html($post['post_title']); ?>
                        </a>
                        <span class="post-date">
                            <?php echo get_the_date('', $post['ID']); ?>
                        </span>
                    </li>
                <?php endforeach; ?>
            </ul>
        </section>

        <!-- Categories Widget -->
        <section class="widget widget_categories">
            <h3 class="widget-title"><?php esc_html_e('Categories', 'technews-theme'); ?></h3>
            <ul>
                <?php
                $categories = get_categories(array(
                    'orderby' => 'count',
                    'order'   => 'DESC',
                    'number'  => 10
                ));
                
                foreach ($categories as $category) :
                ?>
                    <li class="cat-item">
                        <a href="<?php echo get_category_link($category->term_id); ?>">
                            <?php echo esc_html($category->name); ?>
                            <span class="post-count">(<?php echo $category->count; ?>)</span>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </section>

        <!-- Popular Posts Widget -->
        <section class="widget widget_popular_posts">
            <h3 class="widget-title"><?php esc_html_e('Popular Posts', 'technews-theme'); ?></h3>
            <ul>
                <?php
                $popular_posts = get_posts(array(
                    'numberposts' => 5,
                    'meta_key'    => 'post_views_count',
                    'orderby'     => 'meta_value_num',
                    'order'       => 'DESC',
                    'post_status' => 'publish'
                ));
                
                // Fallback to recent posts if no view count meta
                if (empty($popular_posts)) {
                    $popular_posts = get_posts(array(
                        'numberposts' => 5,
                        'orderby'     => 'comment_count',
                        'order'       => 'DESC',
                        'post_status' => 'publish'
                    ));
                }
                
                foreach ($popular_posts as $post) :
                    setup_postdata($post);
                ?>
                    <li class="popular-post-item">
                        <?php if (has_post_thumbnail($post->ID)) : ?>
                            <div class="popular-post-thumbnail">
                                <a href="<?php echo get_permalink($post->ID); ?>">
                                    <?php echo get_the_post_thumbnail($post->ID, 'technews-small'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="popular-post-content">
                            <h4 class="popular-post-title">
                                <a href="<?php echo get_permalink($post->ID); ?>">
                                    <?php echo get_the_title($post->ID); ?>
                                </a>
                            </h4>
                            <span class="popular-post-date">
                                <?php echo get_the_date('', $post->ID); ?>
                            </span>
                        </div>
                    </li>
                <?php 
                endforeach;
                wp_reset_postdata();
                ?>
            </ul>
        </section>

        <!-- Tags Widget -->
        <section class="widget widget_tag_cloud">
            <h3 class="widget-title"><?php esc_html_e('Tags', 'technews-theme'); ?></h3>
            <?php
            $tags = get_tags(array(
                'orderby' => 'count',
                'order'   => 'DESC',
                'number'  => 20
            ));
            
            if (!empty($tags)) :
                echo '<div class="tagcloud">';
                foreach ($tags as $tag) :
                    $tag_link = get_tag_link($tag->term_id);
                    echo '<a href="' . esc_url($tag_link) . '" class="tag-cloud-link tag-link-' . $tag->term_id . '" title="' . esc_attr($tag->count) . ' topics" style="font-size: ' . (8 + ($tag->count * 2)) . 'pt;">';
                    echo esc_html($tag->name);
                    echo '</a> ';
                endforeach;
                echo '</div>';
            endif;
            ?>
        </section>

        <!-- Newsletter Signup (if enabled) -->
        <?php if (get_theme_mod('show_newsletter_widget', false)) : ?>
            <section class="widget widget_newsletter">
                <h3 class="widget-title"><?php esc_html_e('Newsletter', 'technews-theme'); ?></h3>
                <div class="newsletter-content">
                    <p><?php echo esc_html(get_theme_mod('newsletter_description', __('Subscribe to our newsletter to get the latest tech news delivered to your inbox.', 'technews-theme'))); ?></p>
                    
                    <form class="newsletter-form" action="#" method="post">
                        <div class="form-group">
                            <label for="newsletter-email" class="screen-reader-text">
                                <?php esc_html_e('Email Address', 'technews-theme'); ?>
                            </label>
                            <input type="email" id="newsletter-email" name="email" placeholder="<?php esc_attr_e('Your email address', 'technews-theme'); ?>" required>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <?php esc_html_e('Subscribe', 'technews-theme'); ?>
                        </button>
                    </form>
                </div>
            </section>
        <?php endif; ?>

    <?php endif; ?>

</aside><!-- #secondary -->
