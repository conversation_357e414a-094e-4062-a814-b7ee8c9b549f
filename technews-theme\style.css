/*
Theme Name: TechNews Pro
Description: A modern, flat design WordPress theme for technology news websites with full RTL support, mobile-first responsive design, and Gutenberg compatibility.
Author: WordPress Theme Developer
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: technews-theme
Domain Path: /languages
Tags: news, technology, responsive, rtl-language-support, accessibility-ready, custom-menu, custom-logo, featured-images, threaded-comments, translation-ready, block-styles, wide-blocks
Requires at least: 5.0
Tested up to: 6.3
Requires PHP: 7.4
*/

/* ==========================================================================
   CSS Reset and Base Styles
   ========================================================================== */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #333333;
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

h1 {
  font-size: 2.5rem;
}
h2 {
  font-size: 2rem;
}
h3 {
  font-size: 1.75rem;
}
h4 {
  font-size: 1.5rem;
}
h5 {
  font-size: 1.25rem;
}
h6 {
  font-size: 1.125rem;
}

p {
  margin-bottom: 1rem;
}

a {
  color: #007cba;
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover,
a:focus {
  color: #005a87;
  text-decoration: underline;
}

/* ==========================================================================
   Layout Structure
   ========================================================================== */

.site {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.site-content {
  flex: 1;
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.content-area {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

/* Two-column layout for larger screens */
@media (min-width: 768px) {
  .content-area.has-sidebar {
    grid-template-columns: 1fr 300px;
  }

  .content-area.sidebar-left {
    grid-template-columns: 300px 1fr;
  }
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

.site-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e1e1e1;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: box-shadow 0.3s ease;
}

.site-header.sticky {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.site-branding {
  display: flex;
  align-items: center;
}

.custom-logo {
  max-height: 50px;
  width: auto;
  margin-right: 1rem;
}

.site-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.site-title a {
  color: #1a1a1a;
  text-decoration: none;
}

.site-description {
  font-size: 0.875rem;
  color: #666666;
  margin: 0;
}

/* ==========================================================================
   Navigation Styles
   ========================================================================== */

.main-navigation {
  position: relative;
}

.nav-menu {
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu.is-open {
  display: block;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border: 1px solid #e1e1e1;
  border-top: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.nav-menu li {
  border-bottom: 1px solid #f0f0f0;
}

.nav-menu li:last-child {
  border-bottom: none;
}

.nav-menu a {
  display: block;
  padding: 1rem;
  color: #333333;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a:focus {
  background-color: #f8f9fa;
  color: #007cba;
}

.menu-toggle {
  background: none;
  border: 1px solid #e1e1e1;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.menu-toggle:hover,
.menu-toggle:focus {
  background-color: #f8f9fa;
}

.menu-toggle span {
  display: block;
  width: 20px;
  height: 2px;
  background-color: #333333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Desktop Navigation */
@media (min-width: 768px) {
  .menu-toggle {
    display: none;
  }

  .nav-menu {
    display: flex !important;
    position: static;
    background: none;
    border: none;
    box-shadow: none;
  }

  .nav-menu li {
    border: none;
    margin-left: 2rem;
  }

  .nav-menu a {
    padding: 0.5rem 0;
    background: none !important;
  }
}

/* ==========================================================================
   Button Styles
   ========================================================================== */

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #007cba;
  color: #ffffff;
  text-decoration: none;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn:hover,
.btn:focus {
  background-color: #005a87;
  color: #ffffff;
  text-decoration: none;
}

.btn-secondary {
  background-color: #6c757d;
}

.btn-secondary:hover,
.btn-secondary:focus {
  background-color: #545b62;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.screen-reader-text {
  clip: rect(1px, 1px, 1px, 1px);
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
}

.screen-reader-text:focus {
  background-color: #f1f1f1;
  border-radius: 3px;
  box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
  clip: auto !important;
  color: #21759b;
  display: block;
  font-size: 14px;
  font-weight: bold;
  height: auto;
  left: 5px;
  line-height: normal;
  padding: 15px 23px 14px;
  text-decoration: none;
  top: 5px;
  width: auto;
  z-index: 100000;
}

.skip-link {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: 999999;
  text-decoration: none;
}

.skip-link:focus {
  left: 6px;
  top: 7px;
  background: #000;
  color: #fff;
  padding: 8px 16px;
  text-decoration: none;
}

.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.mb-1 {
  margin-bottom: 0.5rem;
}
.mb-2 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 1.5rem;
}
.mb-4 {
  margin-bottom: 2rem;
}

.mt-1 {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 1.5rem;
}
.mt-4 {
  margin-top: 2rem;
}

/* ==========================================================================
   News Grid Layout
   ========================================================================== */

.news-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.featured-post {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.featured-post:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.featured-post-thumbnail {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.featured-post-thumbnail img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.featured-post:hover .featured-post-thumbnail img {
  transform: scale(1.05);
}

.post-category-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: #007cba;
  color: #ffffff;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.post-category-badge a {
  color: inherit;
  text-decoration: none;
}

.grid-post {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.grid-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.grid-post-thumbnail {
  position: relative;
  overflow: hidden;
}

.grid-post-thumbnail img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.grid-post:hover .grid-post-thumbnail img {
  transform: scale(1.05);
}

.grid-post-content {
  padding: 1.5rem;
}

.grid-post .entry-title {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.grid-post .entry-title a {
  color: #1a1a1a;
  text-decoration: none;
  transition: color 0.3s ease;
}

.grid-post .entry-title a:hover {
  color: #007cba;
}

/* Responsive Grid Layout */
@media (min-width: 768px) {
  .news-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .featured-post {
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }
}

@media (min-width: 1024px) {
  .news-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .featured-post {
    grid-column: 1 / -1;
  }
}

@media (min-width: 1200px) {
  .news-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* ==========================================================================
   Post Meta Styles
   ========================================================================== */

.entry-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: #666666;
  margin-bottom: 1rem;
}

.entry-meta a {
  color: #666666;
  text-decoration: none;
  transition: color 0.3s ease;
}

.entry-meta a:hover {
  color: #007cba;
}

.entry-meta .posted-on,
.entry-meta .byline,
.entry-meta .comments-link,
.entry-meta .reading-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.entry-meta .posted-on::before {
  content: '📅';
  font-size: 0.75rem;
}

.entry-meta .byline::before {
  content: '👤';
  font-size: 0.75rem;
}

.entry-meta .comments-link::before {
  content: '💬';
  font-size: 0.75rem;
}

.entry-meta .reading-time::before {
  content: '⏱️';
  font-size: 0.75rem;
}

.entry-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.category-link {
  background-color: #f8f9fa;
  color: #333333;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.category-link:hover {
  background-color: #007cba;
  color: #ffffff;
}

/* ==========================================================================
   Footer Styles
   ========================================================================== */

.site-footer {
  background-color: #1a1a1a;
  color: #ffffff;
  margin-top: auto;
}

.footer-widgets {
  padding: 3rem 0;
  border-bottom: 1px solid #333333;
}

.footer-widget-grid {
  display: grid;
  gap: 2rem;
}

.footer-widgets-1 {
  grid-template-columns: 1fr;
}

.footer-widgets-2 {
  grid-template-columns: repeat(2, 1fr);
}

.footer-widgets-3 {
  grid-template-columns: repeat(3, 1fr);
}

.footer-widgets-4 {
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 768px) {
  .footer-widgets-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.footer-widget-area .widget-title {
  color: #ffffff;
  font-size: 1.125rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007cba;
}

.footer-widget-area .widget {
  margin-bottom: 2rem;
}

.footer-widget-area .widget:last-child {
  margin-bottom: 0;
}

.footer-widget-area a {
  color: #cccccc;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-widget-area a:hover {
  color: #007cba;
}

.footer-widget-area ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-widget-area li {
  margin-bottom: 0.5rem;
  padding-left: 1rem;
  position: relative;
}

.footer-widget-area li::before {
  content: '▶';
  position: absolute;
  left: 0;
  color: #007cba;
  font-size: 0.75rem;
}

.footer-bottom {
  padding: 2rem 0;
  background-color: #0f0f0f;
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: center;
}

@media (min-width: 768px) {
  .footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }
}

.site-info {
  font-size: 0.875rem;
  color: #cccccc;
}

.site-info a {
  color: #ffffff;
  text-decoration: none;
}

.site-info a:hover {
  color: #007cba;
}

.theme-credit {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  opacity: 0.8;
}

.footer-navigation .footer-nav-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

@media (min-width: 768px) {
  .footer-navigation .footer-nav-menu {
    justify-content: flex-end;
  }
}

.footer-navigation a {
  color: #cccccc;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.footer-navigation a:hover {
  color: #007cba;
}

.social-navigation .social-links-menu {
  display: flex;
  justify-content: center;
  gap: 1rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.social-navigation a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #333333;
  color: #ffffff;
  border-radius: 50%;
  text-decoration: none;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.social-navigation a:hover {
  background-color: #007cba;
  transform: translateY(-2px);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background-color: #007cba;
  color: #ffffff;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: none;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s ease, transform 0.3s ease;
  z-index: 1000;
}

.back-to-top:hover {
  background-color: #005a87;
  transform: translateY(-2px);
}

.back-to-top.show {
  display: flex;
}

/* ==========================================================================
   Sidebar Styles
   ========================================================================== */

.widget-area {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.widget {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #e1e1e1;
}

.widget:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.widget-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007cba;
  color: #1a1a1a;
}

.widget ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.widget li {
  margin-bottom: 0.75rem;
  padding-left: 1rem;
  position: relative;
}

.widget li::before {
  content: '▶';
  position: absolute;
  left: 0;
  color: #007cba;
  font-size: 0.75rem;
}

.widget a {
  color: #333333;
  text-decoration: none;
  transition: color 0.3s ease;
}

.widget a:hover {
  color: #007cba;
}

.popular-post-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.popular-post-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.popular-post-thumbnail {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  overflow: hidden;
  border-radius: 4px;
}

.popular-post-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.popular-post-content {
  flex: 1;
}

.popular-post-title {
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.popular-post-title a {
  color: #1a1a1a;
  text-decoration: none;
}

.popular-post-title a:hover {
  color: #007cba;
}

.popular-post-date {
  font-size: 0.75rem;
  color: #666666;
}

.tagcloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-cloud-link {
  background-color: #f8f9fa;
  color: #333333;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  text-decoration: none;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.tag-cloud-link:hover {
  background-color: #007cba;
  color: #ffffff;
}

/* Newsletter Widget */
.newsletter-form {
  margin-top: 1rem;
}

.newsletter-form .form-group {
  margin-bottom: 1rem;
}

.newsletter-form input[type='email'] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.newsletter-form input[type='email']:focus {
  outline: none;
  border-color: #007cba;
}

/* ==========================================================================
   Single Post Styles
   ========================================================================== */

.single-post {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 3rem;
}

.single-post-thumbnail {
  position: relative;
  overflow: hidden;
}

.single-post-thumbnail img {
  width: 100%;
  height: auto;
}

.wp-caption-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: #ffffff;
  padding: 1rem;
  font-size: 0.875rem;
  font-style: italic;
}

.single-post-content {
  padding: 2rem;
}

.single-post .entry-title {
  font-size: 2.5rem;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.single-post .entry-content {
  font-size: 1.125rem;
  line-height: 1.7;
  color: #333333;
}

.single-post .entry-content p {
  margin-bottom: 1.5rem;
}

.single-post .entry-content h2,
.single-post .entry-content h3,
.single-post .entry-content h4,
.single-post .entry-content h5,
.single-post .entry-content h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.single-post .entry-content blockquote {
  background-color: #f8f9fa;
  border-left: 4px solid #007cba;
  padding: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  font-size: 1.25rem;
  border-radius: 0 8px 8px 0;
}

.single-post .entry-content code {
  background-color: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.single-post .entry-content pre {
  background-color: #1a1a1a;
  color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 2rem 0;
}

.single-post .entry-content pre code {
  background: none;
  padding: 0;
  color: inherit;
}

/* Social Sharing */
.social-sharing {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.social-share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  text-decoration: none;
  color: #ffffff;
  transition: transform 0.3s ease;
}

.social-share-btn:hover {
  transform: translateY(-2px);
}

.social-share-btn.facebook {
  background-color: #1877f2;
}

.social-share-btn.twitter {
  background-color: #1da1f2;
}

.social-share-btn.linkedin {
  background-color: #0077b5;
}

.social-share-btn.email {
  background-color: #666666;
}

/* ==========================================================================
   Author Bio Styles
   ========================================================================== */

.author-bio {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
  margin: 3rem 0;
}

.author-bio-content {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.author-avatar {
  flex-shrink: 0;
}

.author-avatar img {
  border-radius: 50%;
  border: 3px solid #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.author-info {
  flex: 1;
}

.author-name {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.author-name a {
  color: #1a1a1a;
  text-decoration: none;
}

.author-name a:hover {
  color: #007cba;
}

.author-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #333333;
  margin-bottom: 1rem;
}

.author-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #666666;
}

.author-posts-link {
  color: #007cba;
  text-decoration: none;
  font-weight: 500;
}

.author-posts-link:hover {
  text-decoration: underline;
}

.author-social {
  display: flex;
  gap: 0.5rem;
}

.author-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #333333;
  color: #ffffff;
  border-radius: 50%;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.author-social a:hover {
  background-color: #007cba;
}

@media (max-width: 767px) {
  .author-bio-content {
    flex-direction: column;
    text-align: center;
  }

  .author-meta {
    justify-content: center;
  }

  .author-social {
    justify-content: center;
  }
}

/* ==========================================================================
   Related Posts Styles
   ========================================================================== */

.related-posts {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
  margin: 3rem 0;
}

.related-posts-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
  color: #1a1a1a;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .related-posts-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .related-posts-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.related-post {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.related-post:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.related-post-thumbnail {
  position: relative;
  overflow: hidden;
}

.related-post-thumbnail img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}

.related-post:hover .related-post-thumbnail img {
  transform: scale(1.05);
}

.related-post-content {
  padding: 1.5rem;
}

.related-post .entry-title {
  font-size: 1.125rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.related-post .entry-title a {
  color: #1a1a1a;
  text-decoration: none;
  transition: color 0.3s ease;
}

.related-post .entry-title a:hover {
  color: #007cba;
}

/* ==========================================================================
   Pagination Styles
   ========================================================================== */

.pagination-wrapper {
  margin: 3rem 0;
  text-align: center;
}

.page-numbers {
  display: inline-flex;
  gap: 0.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.page-numbers .page-numbers {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  padding: 0.5rem;
  background-color: #ffffff;
  color: #333333;
  text-decoration: none;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.page-numbers .page-numbers:hover,
.page-numbers .page-numbers.current {
  background-color: #007cba;
  color: #ffffff;
  border-color: #007cba;
}

.page-numbers .prev,
.page-numbers .next {
  padding: 0.5rem 1rem;
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.search-form {
  position: relative;
  display: flex;
  max-width: 400px;
}

.search-form-wrapper {
  position: relative;
  display: flex;
  width: 100%;
}

.search-field {
  flex: 1;
  padding: 0.75rem 3rem 0.75rem 1rem;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-field:focus {
  outline: none;
  border-color: #007cba;
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.search-submit {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 3rem;
  background: none;
  border: none;
  color: #666666;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-submit:hover,
.search-submit:focus {
  color: #007cba;
  outline: none;
}

/* Comment Form */
.comment-form {
  margin-top: 2rem;
}

.comment-form-comment,
.comment-form-author,
.comment-form-email,
.comment-form-url {
  margin-bottom: 1rem;
}

.comment-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333333;
}

.comment-form input[type='text'],
.comment-form input[type='email'],
.comment-form input[type='url'],
.comment-form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.comment-form input[type='text']:focus,
.comment-form input[type='email']:focus,
.comment-form input[type='url']:focus,
.comment-form textarea:focus {
  outline: none;
  border-color: #007cba;
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.comment-form textarea {
  min-height: 120px;
  resize: vertical;
}

.form-submit {
  margin-top: 1rem;
}

.required {
  color: #e74c3c;
}

/* ==========================================================================
   Comments Styles
   ========================================================================== */

.comments-area {
  margin-top: 3rem;
  padding-top: 3rem;
  border-top: 1px solid #e1e1e1;
}

.comments-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: #1a1a1a;
}

.comment-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.comment {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.comment .children {
  margin-top: 1.5rem;
  margin-left: 2rem;
  list-style: none;
  padding: 0;
}

.comment-body {
  position: relative;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.comment-author img {
  border-radius: 50%;
  flex-shrink: 0;
}

.comment-metadata {
  flex: 1;
}

.comment-metadata .fn {
  font-weight: 600;
  font-style: normal;
  color: #1a1a1a;
}

.comment-metadata a {
  color: #666666;
  text-decoration: none;
  font-size: 0.875rem;
}

.comment-metadata a:hover {
  color: #007cba;
}

.comment-content {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.comment-awaiting-moderation {
  color: #f39c12;
  font-style: italic;
}

.reply {
  text-align: right;
}

.comment-reply-link {
  color: #007cba;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.comment-reply-link:hover {
  text-decoration: underline;
}

.no-comments {
  text-align: center;
  color: #666666;
  font-style: italic;
  margin: 2rem 0;
}
