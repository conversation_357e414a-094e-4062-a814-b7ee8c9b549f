/*
Theme Name: TechNews Pro
Description: RTL (Right-to-Left) language support styles for TechNews Pro theme
Author: WordPress Theme Developer
Version: 1.0.0
*/

/* ==========================================================================
   RTL Base Styles
   ========================================================================== */

body {
    direction: rtl;
    text-align: right;
}

/* ==========================================================================
   RTL Layout Adjustments
   ========================================================================== */

.header-container {
    direction: rtl;
}

.site-branding {
    flex-direction: row-reverse;
}

.custom-logo {
    margin-right: 0;
    margin-left: 1rem;
}

/* Navigation RTL */
.main-navigation {
    direction: rtl;
}

.nav-menu {
    direction: rtl;
}

.nav-menu li {
    margin-left: 0;
    margin-right: 2rem;
}

.menu-toggle {
    direction: rtl;
}

/* Content Area RTL */
.content-area.has-sidebar {
    grid-template-columns: 300px 1fr;
}

.content-area.sidebar-left {
    grid-template-columns: 1fr 300px;
}

/* ==========================================================================
   RTL Typography and Text Direction
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    text-align: right;
}

p {
    text-align: right;
}

blockquote {
    border-left: none;
    border-right: 4px solid #007cba;
    padding-left: 0;
    padding-right: 1.5rem;
    text-align: right;
}

/* ==========================================================================
   RTL Grid Layout
   ========================================================================== */

.news-grid {
    direction: rtl;
}

.featured-post {
    direction: rtl;
    text-align: right;
}

.grid-post {
    direction: rtl;
    text-align: right;
}

/* ==========================================================================
   RTL Post Meta
   ========================================================================== */

.entry-meta {
    direction: rtl;
    justify-content: flex-start;
}

.entry-categories {
    direction: rtl;
    justify-content: flex-start;
}

.post-category-badge {
    left: auto;
    right: 1rem;
}

/* ==========================================================================
   RTL Navigation and Menus
   ========================================================================== */

.nav-menu a {
    text-align: right;
}

.footer-navigation .footer-nav-menu {
    direction: rtl;
}

.social-navigation .social-links-menu {
    direction: rtl;
}

/* ==========================================================================
   RTL Sidebar and Widgets
   ========================================================================== */

.widget-area {
    direction: rtl;
    text-align: right;
}

.widget li {
    padding-left: 0;
    padding-right: 1rem;
}

.widget li::before {
    left: auto;
    right: 0;
    content: "◀";
}

.popular-post-item {
    direction: rtl;
}

.popular-post-thumbnail {
    order: 2;
}

.popular-post-content {
    order: 1;
    text-align: right;
}

.tagcloud {
    direction: rtl;
    justify-content: flex-start;
}

/* ==========================================================================
   RTL Single Post
   ========================================================================== */

.single-post {
    direction: rtl;
}

.single-post-content {
    direction: rtl;
    text-align: right;
}

.single-post .entry-title {
    text-align: right;
}

.single-post .entry-content {
    text-align: right;
    direction: rtl;
}

.single-post .entry-content blockquote {
    border-left: none;
    border-right: 4px solid #007cba;
    border-radius: 8px 0 0 8px;
}

/* ==========================================================================
   RTL Author Bio
   ========================================================================== */

.author-bio {
    direction: rtl;
    text-align: right;
}

.author-bio-content {
    direction: rtl;
}

.author-avatar {
    order: 2;
}

.author-info {
    order: 1;
    text-align: right;
}

.author-meta {
    direction: rtl;
    justify-content: flex-start;
}

.author-social {
    direction: rtl;
    justify-content: flex-start;
}

/* ==========================================================================
   RTL Related Posts
   ========================================================================== */

.related-posts {
    direction: rtl;
    text-align: right;
}

.related-posts-grid {
    direction: rtl;
}

.related-post {
    direction: rtl;
    text-align: right;
}

.related-post-content {
    text-align: right;
}

/* ==========================================================================
   RTL Footer
   ========================================================================== */

.site-footer {
    direction: rtl;
    text-align: right;
}

.footer-widget-grid {
    direction: rtl;
}

.footer-widget-area {
    text-align: right;
}

.footer-widget-area li {
    padding-left: 0;
    padding-right: 1rem;
}

.footer-widget-area li::before {
    left: auto;
    right: 0;
    content: "◀";
}

.footer-bottom-content {
    direction: rtl;
}

.site-info {
    text-align: right;
}

/* ==========================================================================
   RTL Forms
   ========================================================================== */

.search-form {
    direction: rtl;
}

.search-field {
    text-align: right;
}

.newsletter-form {
    direction: rtl;
}

.newsletter-form input[type="email"] {
    text-align: right;
}

/* ==========================================================================
   RTL Archive and Search
   ========================================================================== */

.archive-controls {
    direction: rtl;
}

.view-toggle {
    order: 2;
}

.sort-options {
    order: 1;
}

.archive-posts-container {
    direction: rtl;
}

.search-results-container {
    direction: rtl;
}

.search-result {
    direction: rtl;
    text-align: right;
}

.search-result-thumbnail {
    order: 2;
}

.search-result-content {
    order: 1;
    text-align: right;
}

/* ==========================================================================
   RTL 404 Page
   ========================================================================== */

.error-404 {
    direction: rtl;
    text-align: right;
}

.error-404-content {
    text-align: right;
}

.error-404-widgets {
    direction: rtl;
}

.error-widget {
    text-align: right;
}

/* ==========================================================================
   RTL Pagination
   ========================================================================== */

.pagination-wrapper {
    direction: rtl;
}

.page-numbers {
    direction: rtl;
}

/* ==========================================================================
   RTL Comments
   ========================================================================== */

.comments-area {
    direction: rtl;
    text-align: right;
}

.comment-list {
    direction: rtl;
}

.comment-body {
    direction: rtl;
    text-align: right;
}

.comment-meta {
    direction: rtl;
}

.comment-content {
    text-align: right;
}

.comment-form {
    direction: rtl;
}

.comment-form-comment textarea {
    text-align: right;
}

/* ==========================================================================
   RTL Responsive Adjustments
   ========================================================================== */

@media (max-width: 767px) {
    .author-bio-content {
        text-align: center;
    }
    
    .author-meta {
        justify-content: center;
    }
    
    .author-social {
        justify-content: center;
    }
    
    .footer-bottom-content {
        text-align: center;
    }
    
    .nav-menu.is-open {
        direction: rtl;
        text-align: right;
    }
    
    .nav-menu a {
        text-align: right;
    }
}

/* ==========================================================================
   RTL Utility Classes
   ========================================================================== */

.rtl .text-left {
    text-align: right !important;
}

.rtl .text-right {
    text-align: left !important;
}

.rtl .float-left {
    float: right !important;
}

.rtl .float-right {
    float: left !important;
}

/* ==========================================================================
   RTL Back to Top Button
   ========================================================================== */

.back-to-top {
    left: 2rem;
    right: auto;
}
