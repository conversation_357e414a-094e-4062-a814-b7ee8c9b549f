<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */
?>

    <footer id="colophon" class="site-footer" role="contentinfo">
        
        <?php
        // Check if any footer widget areas have widgets
        $has_footer_widgets = false;
        for ($i = 1; $i <= 4; $i++) {
            if (is_active_sidebar('footer-' . $i)) {
                $has_footer_widgets = true;
                break;
            }
        }
        ?>

        <?php if ($has_footer_widgets) : ?>
            <div class="footer-widgets">
                <div class="container">
                    <div class="footer-widget-areas">
                        <?php
                        $active_widgets = 0;
                        for ($i = 1; $i <= 4; $i++) {
                            if (is_active_sidebar('footer-' . $i)) {
                                $active_widgets++;
                            }
                        }
                        
                        $widget_class = '';
                        switch ($active_widgets) {
                            case 1:
                                $widget_class = 'footer-widgets-1';
                                break;
                            case 2:
                                $widget_class = 'footer-widgets-2';
                                break;
                            case 3:
                                $widget_class = 'footer-widgets-3';
                                break;
                            case 4:
                            default:
                                $widget_class = 'footer-widgets-4';
                                break;
                        }
                        ?>
                        
                        <div class="footer-widget-grid <?php echo esc_attr($widget_class); ?>">
                            <?php for ($i = 1; $i <= 4; $i++) : ?>
                                <?php if (is_active_sidebar('footer-' . $i)) : ?>
                                    <div class="footer-widget-area footer-widget-<?php echo $i; ?>">
                                        <?php dynamic_sidebar('footer-' . $i); ?>
                                    </div>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Footer Bottom Bar -->
        <div class="footer-bottom">
            <div class="container">
                <div class="footer-bottom-content">
                    
                    <!-- Copyright Information -->
                    <div class="site-info">
                        <?php
                        $copyright_text = get_theme_mod('copyright_text', '');
                        if (!empty($copyright_text)) :
                            echo wp_kses_post($copyright_text);
                        else :
                        ?>
                            <p>
                                &copy; <?php echo date('Y'); ?> 
                                <a href="<?php echo esc_url(home_url('/')); ?>">
                                    <?php bloginfo('name'); ?>
                                </a>
                                <?php esc_html_e('All rights reserved.', 'technews-theme'); ?>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (get_theme_mod('show_theme_credit', true)) : ?>
                            <p class="theme-credit">
                                <?php
                                printf(
                                    esc_html__('Powered by %1$s | Theme: %2$s', 'technews-theme'),
                                    '<a href="' . esc_url(__('https://wordpress.org/', 'technews-theme')) . '">WordPress</a>',
                                    '<a href="' . esc_url('https://github.com/technews-theme/') . '">TechNews Pro</a>'
                                );
                                ?>
                            </p>
                        <?php endif; ?>
                    </div>

                    <!-- Footer Navigation Menu -->
                    <?php if (has_nav_menu('footer')) : ?>
                        <nav class="footer-navigation" role="navigation" aria-label="<?php esc_attr_e('Footer Menu', 'technews-theme'); ?>">
                            <?php
                            wp_nav_menu(array(
                                'theme_location' => 'footer',
                                'menu_id'        => 'footer-menu',
                                'menu_class'     => 'footer-nav-menu',
                                'container'      => false,
                                'depth'          => 1,
                            ));
                            ?>
                        </nav>
                    <?php endif; ?>

                    <!-- Social Links Menu -->
                    <?php if (has_nav_menu('social')) : ?>
                        <div class="social-navigation">
                            <?php
                            wp_nav_menu(array(
                                'theme_location' => 'social',
                                'menu_id'        => 'social-menu',
                                'menu_class'     => 'social-links-menu',
                                'container'      => false,
                                'depth'          => 1,
                                'link_before'    => '<span class="screen-reader-text">',
                                'link_after'     => '</span>',
                            ));
                            ?>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <?php if (get_theme_mod('show_back_to_top', true)) : ?>
            <button id="back-to-top" class="back-to-top" aria-label="<?php esc_attr_e('Back to top', 'technews-theme'); ?>">
                <span class="screen-reader-text"><?php esc_html_e('Back to top', 'technews-theme'); ?></span>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 19V5M5 12L12 5L19 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        <?php endif; ?>

    </footer>

</div><!-- #page -->

<?php wp_footer(); ?>

</body>
</html>
