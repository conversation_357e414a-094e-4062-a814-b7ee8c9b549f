<?php
/**
 * Template part for displaying author bio
 *
 * @package TechNews_Theme
 * @since 1.0.0
 */

$author_id = get_the_author_meta('ID');
$author_description = get_the_author_meta('description');

// Only show if author has a description
if (empty($author_description)) {
    return;
}
?>

<div class="author-bio">
    <div class="author-bio-content">
        
        <div class="author-avatar">
            <?php echo get_avatar($author_id, 80); ?>
        </div>

        <div class="author-info">
            <h3 class="author-name">
                <a href="<?php echo esc_url(get_author_posts_url($author_id)); ?>">
                    <?php echo get_the_author(); ?>
                </a>
            </h3>

            <div class="author-description">
                <?php echo wp_kses_post($author_description); ?>
            </div>

            <div class="author-meta">
                <span class="author-posts-count">
                    <?php
                    $post_count = count_user_posts($author_id);
                    printf(
                        _n('%s Article', '%s Articles', $post_count, 'technews-theme'),
                        number_format_i18n($post_count)
                    );
                    ?>
                </span>

                <a href="<?php echo esc_url(get_author_posts_url($author_id)); ?>" class="author-posts-link">
                    <?php esc_html_e('View all posts', 'technews-theme'); ?>
                </a>
            </div>

            <?php
            // Author social links (if available)
            $author_website = get_the_author_meta('user_url');
            $author_twitter = get_the_author_meta('twitter');
            $author_facebook = get_the_author_meta('facebook');
            $author_linkedin = get_the_author_meta('linkedin');

            if ($author_website || $author_twitter || $author_facebook || $author_linkedin) :
            ?>
                <div class="author-social">
                    <?php if ($author_website) : ?>
                        <a href="<?php echo esc_url($author_website); ?>" target="_blank" rel="noopener noreferrer" class="author-website">
                            <span class="screen-reader-text"><?php esc_html_e('Website', 'technews-theme'); ?></span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <?php if ($author_twitter) : ?>
                        <a href="https://twitter.com/<?php echo esc_attr($author_twitter); ?>" target="_blank" rel="noopener noreferrer" class="author-twitter">
                            <span class="screen-reader-text"><?php esc_html_e('Twitter', 'technews-theme'); ?></span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <?php if ($author_facebook) : ?>
                        <a href="https://facebook.com/<?php echo esc_attr($author_facebook); ?>" target="_blank" rel="noopener noreferrer" class="author-facebook">
                            <span class="screen-reader-text"><?php esc_html_e('Facebook', 'technews-theme'); ?></span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                    <?php endif; ?>

                    <?php if ($author_linkedin) : ?>
                        <a href="https://linkedin.com/in/<?php echo esc_attr($author_linkedin); ?>" target="_blank" rel="noopener noreferrer" class="author-linkedin">
                            <span class="screen-reader-text"><?php esc_html_e('LinkedIn', 'technews-theme'); ?></span>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        </div>

    </div>
</div>
