import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white py-8 mt-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">محول يوتيوب</h3>
            <p className="text-gray-300 text-sm">
              أفضل أداة مجانية لتحويل فيديوهات يوتيوب إلى ملفات MP3 و MP4 بجودة عالية.
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">روابط مفيدة</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">الأسئلة الشائعة</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">شروط الاستخدام</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">سياسة الخصوصية</a></li>
              <li><a href="#" className="text-gray-300 hover:text-white transition-colors">اتصل بنا</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">معلومات</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>✓ مجاني 100%</li>
              <li>✓ لا يتطلب تسجيل</li>
              <li>✓ جودة عالية</li>
              <li>✓ سريع وآمن</li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-6 text-center">
          <p className="text-gray-400 text-sm">
            © 2024 محول يوتيوب. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

