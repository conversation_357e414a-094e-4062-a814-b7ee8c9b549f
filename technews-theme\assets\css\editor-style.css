/*
Theme Name: TechNews Pro
Description: Editor styles for TechNews Pro theme
Author: WordPress Theme Developer
Version: 1.0.0
*/

/* ==========================================================================
   Editor Base Styles
   ========================================================================== */

.editor-styles-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333333;
    background-color: #ffffff;
}

/* ==========================================================================
   Typography
   ========================================================================== */

.editor-styles-wrapper h1,
.editor-styles-wrapper h2,
.editor-styles-wrapper h3,
.editor-styles-wrapper h4,
.editor-styles-wrapper h5,
.editor-styles-wrapper h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.editor-styles-wrapper h1 { font-size: 2.5rem; }
.editor-styles-wrapper h2 { font-size: 2rem; }
.editor-styles-wrapper h3 { font-size: 1.75rem; }
.editor-styles-wrapper h4 { font-size: 1.5rem; }
.editor-styles-wrapper h5 { font-size: 1.25rem; }
.editor-styles-wrapper h6 { font-size: 1.125rem; }

.editor-styles-wrapper p {
    margin-bottom: 1rem;
}

.editor-styles-wrapper a {
    color: #007cba;
    text-decoration: none;
}

.editor-styles-wrapper a:hover {
    color: #005a87;
    text-decoration: underline;
}

/* ==========================================================================
   Block Editor Specific Styles
   ========================================================================== */

.editor-styles-wrapper .wp-block {
    max-width: 100%;
}

/* Post Title */
.editor-post-title__input {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: #1a1a1a;
    margin-bottom: 1rem;
}

/* Block Toolbar */
.block-editor-block-toolbar {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

/* ==========================================================================
   Paragraph Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-paragraph {
    margin-bottom: 1.5rem;
}

.editor-styles-wrapper .wp-block-paragraph.has-drop-cap:not(:focus)::first-letter {
    float: left;
    font-size: 8.4rem;
    line-height: 0.68;
    font-weight: 100;
    margin: 0.05em 0.1em 0 0;
    text-transform: uppercase;
    font-style: normal;
    color: #007cba;
}

/* ==========================================================================
   Quote Blocks
   ========================================================================== */

.editor-styles-wrapper .wp-block-quote {
    background-color: #f8f9fa;
    border-left: 4px solid #007cba;
    padding: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
}

.editor-styles-wrapper .wp-block-quote p {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.editor-styles-wrapper .wp-block-quote cite {
    display: block;
    font-size: 0.875rem;
    font-style: normal;
    color: #666666;
    margin-top: 1rem;
}

.editor-styles-wrapper .wp-block-pullquote {
    padding: 3rem 0;
    margin: 2rem 0;
    text-align: center;
    border-top: 4px solid #007cba;
    border-bottom: 4px solid #007cba;
}

.editor-styles-wrapper .wp-block-pullquote blockquote {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-size: 1.75rem;
    line-height: 1.4;
    font-weight: 600;
}

/* ==========================================================================
   List Blocks
   ========================================================================== */

.editor-styles-wrapper .wp-block-list {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.editor-styles-wrapper .wp-block-list li {
    margin-bottom: 0.5rem;
}

/* ==========================================================================
   Button Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-button .wp-block-button__link {
    background-color: #007cba;
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
}

.editor-styles-wrapper .wp-block-button.is-style-outline .wp-block-button__link {
    background-color: transparent;
    color: #007cba;
    border: 2px solid #007cba;
}

/* ==========================================================================
   Code Blocks
   ========================================================================== */

.editor-styles-wrapper .wp-block-code {
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 2rem;
}

.editor-styles-wrapper .wp-block-code code {
    background: none;
    color: inherit;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    padding: 0;
}

.editor-styles-wrapper .wp-block-preformatted {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 2rem;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
}

/* ==========================================================================
   Table Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-table table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.editor-styles-wrapper .wp-block-table th,
.editor-styles-wrapper .wp-block-table td {
    padding: 0.75rem;
    border: 1px solid #e1e1e1;
    text-align: left;
}

.editor-styles-wrapper .wp-block-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* ==========================================================================
   Image Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-image img {
    height: auto;
    max-width: 100%;
    border-radius: 8px;
}

.editor-styles-wrapper .wp-block-image figcaption {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #666666;
    text-align: center;
    font-style: italic;
}

/* ==========================================================================
   Cover Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-cover {
    border-radius: 8px;
    overflow: hidden;
}

.editor-styles-wrapper .wp-block-cover .wp-block-cover__inner-container {
    padding: 3rem;
}

/* ==========================================================================
   Group Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-group.has-background {
    padding: 2rem;
    border-radius: 8px;
}

/* ==========================================================================
   Separator Block
   ========================================================================== */

.editor-styles-wrapper .wp-block-separator {
    border: none;
    border-top: 1px solid #e1e1e1;
    margin: 3rem auto;
    max-width: 100px;
}

.editor-styles-wrapper .wp-block-separator.is-style-wide {
    max-width: 100%;
}

.editor-styles-wrapper .wp-block-separator.is-style-dots {
    border: none;
    text-align: center;
    max-width: none;
    line-height: 1;
    height: auto;
}

.editor-styles-wrapper .wp-block-separator.is-style-dots::before {
    content: "···";
    color: #666666;
    font-size: 1.5rem;
    letter-spacing: 2rem;
    padding-left: 2rem;
}

/* ==========================================================================
   Color Classes
   ========================================================================== */

.editor-styles-wrapper .has-primary-blue-color {
    color: #007cba;
}

.editor-styles-wrapper .has-primary-blue-background-color {
    background-color: #007cba;
}

.editor-styles-wrapper .has-dark-blue-color {
    color: #005a87;
}

.editor-styles-wrapper .has-dark-blue-background-color {
    background-color: #005a87;
}

.editor-styles-wrapper .has-light-gray-color {
    color: #f8f9fa;
}

.editor-styles-wrapper .has-light-gray-background-color {
    background-color: #f8f9fa;
}

.editor-styles-wrapper .has-dark-gray-color {
    color: #333333;
}

.editor-styles-wrapper .has-dark-gray-background-color {
    background-color: #333333;
}

/* ==========================================================================
   Wide and Full Width
   ========================================================================== */

.editor-styles-wrapper .alignwide {
    margin-left: auto;
    margin-right: auto;
    clear: both;
}

.editor-styles-wrapper .alignfull {
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
    width: auto;
    max-width: 1000%;
    clear: both;
}
