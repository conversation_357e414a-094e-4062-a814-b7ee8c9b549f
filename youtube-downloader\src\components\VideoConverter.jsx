import React, { useState } from 'react';
import { Button } from '@/components/ui/button.jsx';
import { Download, Loader2, Music, Video, CheckCircle, AlertCircle } from 'lucide-react';

const VideoConverter = () => {
  const [url, setUrl] = useState('');
  const [format, setFormat] = useState('mp3');
  const [isLoading, setIsLoading] = useState(false);
  const [conversionId, setConversionId] = useState('');
  const [progress, setProgress] = useState(0);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [error, setError] = useState('');
  const [status, setStatus] = useState('');

  const API_BASE = "https://8xhpiqce06m0.manus.space";

  const checkStatus = async (id) => {
    try {
      const response = await fetch(`${API_BASE}/api/status/${id}`);
      const data = await response.json();
      
      if (response.ok) {
        setProgress(data.progress || 0);
        setStatus(data.status);
        
        if (data.status === 'completed' && data.downloadUrl) {
          setDownloadUrl(`${API_BASE}${data.downloadUrl}`);
          setIsLoading(false);
          return true;
        } else if (data.status === 'error') {
          setError(data.error || 'حدث خطأ أثناء التحويل');
          setIsLoading(false);
          return true;
        }
      }
      return false;
    } catch (err) {
      console.error('Error checking status:', err);
      return false;
    }
  };

  const pollStatus = async (id) => {
    const maxAttempts = 60; // 5 دقائق كحد أقصى
    let attempts = 0;
    
    const poll = async () => {
      attempts++;
      const completed = await checkStatus(id);
      
      if (!completed && attempts < maxAttempts) {
        setTimeout(poll, 5000); // فحص كل 5 ثوان
      } else if (attempts >= maxAttempts) {
        setError('انتهت مهلة التحويل. يرجى المحاولة مرة أخرى.');
        setIsLoading(false);
      }
    };
    
    poll();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setDownloadUrl('');
    setProgress(0);
    setStatus('');
    
    if (!url.trim()) {
      setError('يرجى إدخال رابط فيديو يوتيوب صحيح');
      return;
    }

    // التحقق من صحة رابط يوتيوب
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+/;
    if (!youtubeRegex.test(url)) {
      setError('يرجى إدخال رابط يوتيوب صحيح');
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await fetch(`${API_BASE}/api/convert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, format }),
      });

      const data = await response.json();

      if (response.ok) {
        setConversionId(data.conversion_id);
        pollStatus(data.conversion_id);
      } else {
        setError(data.error || 'فشل في بدء التحويل');
        setIsLoading(false);
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.');
      setIsLoading(false);
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'processing':
        return 'جاري معالجة الفيديو...';
      case 'downloading':
        return 'جاري تحميل الفيديو...';
      case 'completed':
        return 'تم التحويل بنجاح!';
      case 'error':
        return 'حدث خطأ أثناء التحويل';
      default:
        return 'جاري التحويل...';
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-12">
      <div className="text-center mb-8">
        <h2 className="text-4xl font-bold text-gray-800 mb-4">
          محول يوتيوب إلى MP3 و MP4
        </h2>
        <p className="text-lg text-gray-600">
          حول فيديوهات يوتيوب إلى ملفات صوتية أو فيديو بجودة عالية مجاناً
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-xl p-8 mb-8">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-2">
              أدخل رابط فيديو يوتيوب
            </label>
            <input
              type="url"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://www.youtube.com/watch?v=..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              disabled={isLoading}
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
            <div className="flex gap-2">
              <Button
                type="button"
                variant={format === 'mp3' ? 'default' : 'outline'}
                onClick={() => setFormat('mp3')}
                className="flex items-center gap-2"
                disabled={isLoading}
              >
                <Music size={20} />
                MP3
              </Button>
              <Button
                type="button"
                variant={format === 'mp4' ? 'default' : 'outline'}
                onClick={() => setFormat('mp4')}
                className="flex items-center gap-2"
                disabled={isLoading}
              >
                <Video size={20} />
                MP4
              </Button>
            </div>
            
            <Button
              type="submit"
              disabled={isLoading || !url.trim()}
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="animate-spin mr-2" size={20} />
                  تحويل
                </>
              ) : (
                'تحويل'
              )}
            </Button>
          </div>
        </form>

        {isLoading && (
          <div className="mt-6 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <span className="text-blue-800 font-medium">{getStatusMessage()}</span>
              <span className="text-blue-600 text-sm">{progress}%</span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg flex items-center gap-2">
            <AlertCircle size={20} />
            {error}
          </div>
        )}

        {downloadUrl && !isLoading && (
          <div className="mt-6 p-6 bg-green-100 border border-green-400 rounded-lg text-center">
            <div className="flex items-center justify-center gap-2 mb-3">
              <CheckCircle className="text-green-600" size={24} />
              <h3 className="text-lg font-semibold text-green-800">
                تم التحويل بنجاح!
              </h3>
            </div>
            <Button
              onClick={() => window.open(downloadUrl, '_blank')}
              className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 mx-auto"
            >
              <Download size={20} />
              تحميل الملف
            </Button>
          </div>
        )}
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">كيفية الاستخدام:</h3>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>ابحث عن الفيديو المطلوب على يوتيوب وانسخ الرابط</li>
          <li>الصق الرابط في الحقل أعلاه</li>
          <li>اختر الصيغة المطلوبة (MP3 للصوت أو MP4 للفيديو)</li>
          <li>اضغط على زر "تحويل" وانتظر حتى اكتمال العملية</li>
          <li>اضغط على زر "تحميل" لحفظ الملف على جهازك</li>
        </ol>
        
        <div className="mt-4 p-4 bg-yellow-100 border border-yellow-400 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>ملاحظة:</strong> يمكن تحويل الفيديوهات التي لا تتجاوز مدتها 90 دقيقة فقط.
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoConverter;

